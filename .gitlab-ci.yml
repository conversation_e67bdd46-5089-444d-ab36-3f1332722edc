.ssh-config: &ssh-config
  - which ssh-agent > /dev/null || apk add openssh-client -y
  - eval `ssh-agent`
  - echo "${DEPLOY_SSH_PRIVATE_KEY}" | tr -d '\r' | ssh-add -
  - mkdir -p ~/.ssh
  - chmod 0700 ~/.ssh
  - echo -e "Host *\n\tStrictHostKeyChecking no\n\n" >> ~/.ssh/config
  - git config --global user.email "<EMAIL>"
  - git config --global user.name "Deploy"
  - git remote set-url --push origin ${GIT_PROJECT_URL}
  - git config diff.renames 0

image: ikateclab/ci:0.16
stages:
  - build

deploy:
  tags:
    - docker
  stage: build
  cache:
    key: yarn-cache-whatsapp-scripts-src
    paths:
       - .yarn-cache/
  variables:
    # Skip Puppeteer browser download to avoid Node.js compatibility issues in CI
    PUPPETEER_SKIP_CHROMIUM_DOWNLOAD: "true"
  before_script:
    - *ssh-config
  script:
    - git checkout -B ${CI_COMMIT_REF_NAME} ${CI_COMMIT_SHA}
    - yarn install --frozen-lockfile --cache-folder .yarn-cache --ignore-engines
    - SEMVER=$([[ $CI_COMMIT_BRANCH == 'master' ]] && echo minor || echo prerelease)
    - MR_ID=$([[ $CI_COMMIT_BRANCH == 'master' ]] && echo '' || echo mr-${CI_MERGE_REQUEST_IID})
    - BRANCH=${CI_COMMIT_BRANCH}
    - yarn version --preid ${MR_ID} --${SEMVER} --message "Bump version"
    - SRC_VERSION=$(awk -F\" '/"version":/ {print $4}' package.json)
    - echo $SRC_VERSION
    - git push origin HEAD:${CI_COMMIT_REF_NAME}
    - git push origin refs/tags/v${SRC_VERSION}
    - git clone ${GIT_DIST_PROJECT_URL} dist || true
    - cd dist
    - git reset --hard && (git checkout ${BRANCH} || git checkout -B ${BRANCH})
    - cd -
    - yarn build
    - cd dist
    - git commit -am "Built from src v${SRC_VERSION}"
    - yarn version --new-version ${SRC_VERSION} --message "Bump version"
    - git push --set-upstream origin ${BRANCH} --tags
  rules:
    - if: '$CI_COMMIT_MESSAGE =~ /(Bump version|^docs|^\[no-build\])/ || $CI_MERGE_REQUEST_TITLE =~ /^WIP:/'
      when: never
    - if: '$CI_PIPELINE_SOURCE =~ "merge_request_event" || $CI_COMMIT_BRANCH == "master"'
      when: on_success