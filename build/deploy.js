const { execSync } = require('child_process')
const argv = require('yargs').argv

const exec = cmd => {
    console.log('==>', cmd)
    return execSync(cmd, { stdio: 'inherit' })
}

const run = async () => {
    const version = argv.v || argv.version || 'minor'
    const message = argv.m || argv.message || 'bump'
    const force = argv.f || argv.force
    const branch = argv.b || argv.branch || 'master'

    const npmVersion = `npm version ${version} -m "${message}" ${force ? '-f' : ''}`

    const cmds = [
        npmVersion,
        '<NAME_EMAIL>:whatsapp-scripts/whatsapp-scripts-dist.git dist || true',
        `cd dist && git reset --hard && git checkout -B ${branch}`,
        'yarn build:all',
        'cd dist',
        'cd dist && git commit -am "rebuild"',
        `cd dist && ${npmVersion}`,
        `cd dist && git push --set-upstream origin ${branch} --tags`,
    ]

    cmds.map(exec)
}

run()
