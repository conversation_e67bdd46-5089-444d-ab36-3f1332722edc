/* global __dirname, require, module*/
const webpack = require('webpack')
const path = require('path')
const pkg = require('../package.json')

const version = pkg.version

const rootPath = path.resolve(__dirname, '..')
const srcPath = path.resolve(rootPath, 'src')
const distPath = path.resolve(rootPath, 'dist')

module.exports = ({ production = false }) => {
  const config = {
    mode: production ? 'production' : 'development',
    entry: path.resolve(srcPath, 'index.js'),
    devtool: 'none',
    target: 'web',
    output: {
      path: distPath,
      filename: 'index.js',
      libraryTarget: 'var',
    },
    module: {
      rules: [
        {
          test: /\.js$/,
          loader: 'babel-loader',
          exclude: /node_modules/,
        },
      ],
    },
    resolve: {
      modules: [
        path.resolve(rootPath, 'node_modules'),
        srcPath,
      ],
      extensions: ['.json', '.js'],
    },
    plugins: [
        new webpack.DefinePlugin({
            'process.env.NODE_ENV': JSON.stringify(production ? 'production' : 'development'),
            'process.env.SRC_VERSION': JSON.stringify(version),
        })
    ],
  }

  return config
}
