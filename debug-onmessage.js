// Script para debugar o problema do onMessage
// Execute este script no console do navegador

console.log('=== DEBUG ONMESSAGE SCRIPT ===');

// 1. Verificar se o __fidelizza__ está disponível
if (!window.__fidelizza__) {
  console.error('❌ window.__fidelizza__ não está disponível!');
  console.log('Aguarde a inicialização completa ou verifique se o script foi carregado corretamente.');
} else {
  console.log('✅ window.__fidelizza__ está disponível');
  
  // 2. Verificar se os módulos de mensagens estão disponíveis
  if (!window.__fidelizza__.messages) {
    console.error('❌ window.__fidelizza__.messages não está disponível!');
  } else {
    console.log('✅ window.__fidelizza__.messages está disponível');
    
    // 3. Verificar funções específicas
    const functions = ['onMessage', 'debugStoreStatus', 'testOnMessageEvent', 'isMessage'];
    functions.forEach(func => {
      if (typeof window.__fidelizza__.messages[func] === 'function') {
        console.log(`✅ ${func} está disponível`);
      } else {
        console.error(`❌ ${func} não está disponível ou não é uma função`);
      }
    });
    
    // 4. Executar debug do Store
    console.log('\n--- EXECUTANDO DEBUG DO STORE ---');
    try {
      window.__fidelizza__.messages.debugStoreStatus();
    } catch (error) {
      console.error('❌ Erro ao executar debugStoreStatus:', error);
    }
    
    // 5. Testar o evento onMessage
    console.log('\n--- TESTANDO EVENTO ONMESSAGE ---');
    try {
      const testResult = window.__fidelizza__.messages.testOnMessageEvent();
      if (testResult) {
        console.log('✅ Teste do onMessage iniciado com sucesso');
        console.log('📝 Aguarde 30 segundos ou envie uma mensagem para testar');
      } else {
        console.error('❌ Falha ao iniciar teste do onMessage');
      }
    } catch (error) {
      console.error('❌ Erro ao executar testOnMessageEvent:', error);
    }
    
    // 6. Configurar um listener de teste simples
    console.log('\n--- CONFIGURANDO LISTENER DE TESTE ---');
    try {
      let messageCount = 0;
      window.__fidelizza__.messages.onMessage((message) => {
        messageCount++;
        console.log(`🔔 MENSAGEM RECEBIDA #${messageCount}:`, {
          id: message?.id?._serialized,
          type: message?.type,
          isNewMsg: message?.isNewMsg,
          from: message?.from?._serialized,
          body: message?.body?.substring(0, 50) + (message?.body?.length > 50 ? '...' : ''),
          timestamp: new Date(message?.t * 1000).toLocaleString()
        });
      });
      console.log('✅ Listener de teste configurado com sucesso');
      console.log('📝 Agora envie uma mensagem para testar');
    } catch (error) {
      console.error('❌ Erro ao configurar listener de teste:', error);
    }
  }
}

// 7. Verificar se o WhatsApp Web está no modo correto
console.log('\n--- VERIFICANDO STATUS DO WHATSAPP ---');
try {
  if (window.__fidelizza__.status) {
    const isOnChatPage = window.__fidelizza__.status.isOnChatPage();
    const isPhoneConnected = window.__fidelizza__.status.isPhoneConnected();
    
    console.log('📱 Status do WhatsApp:', {
      isOnChatPage,
      isPhoneConnected,
      mode: window.__fidelizza__.status.getMode?.() || 'unknown'
    });
    
    if (!isOnChatPage) {
      console.warn('⚠️ WhatsApp não está na página de chat (modo MAIN)');
    }
    
    if (!isPhoneConnected) {
      console.warn('⚠️ Telefone não está conectado');
    }
  }
} catch (error) {
  console.error('❌ Erro ao verificar status do WhatsApp:', error);
}

console.log('\n=== FIM DO DEBUG ===');
console.log('📝 Para testar, envie uma mensagem para qualquer chat e verifique os logs acima.');
