{"name": "whatsapp-scripts", "version": "1.307.0", "main": "index.js", "private": true, "scripts": {"build:dev": "NODE_OPTIONS=--openssl-legacy-provider webpack --config=build/webpack.config.dev.js", "build": "webpack --config=build/webpack.config.prod.js", "deploy": "node build/deploy.js $*", "test:whatsapp": "npm run build && node tests/whatsapp-puppeteer-test.js"}, "dependencies": {"async": "3.2.5", "eventemitter3": "4.0.7", "moize": "5.4.7", "semver": "7.6.0", "yargs": "14.2.3", "yarn": "^1.22.22"}, "devDependencies": {"@babel/core": "7.24.3", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/preset-env": "7.24.3", "@babel/preset-stage-3": "7.8.3", "@types/semver": "7.5.8", "babel-loader": "8.3.0", "compare-versions": "^6.1.1", "puppeteer": "^24.10.0", "webpack": "4.47.0", "webpack-cli": "4.10.0"}}