import pooling from './modules/utils/polling'

let originalClearCredentialsAndStoredData

export const initial = () => pooling(
  async () => {
      window.require('WAWebIsOfficialClient').isOfficialClient = true
      window.require('WAWebIsOfficialClient').isLegitErrorStack = () => true

      const WASocket = window.require('WAWebSocketModel').Socket
      originalClearCredentialsAndStoredData = originalClearCredentialsAndStoredData
        || WASocket.clearCredentialsAndStoredData.bind(WASocket)
      WASocket.clearCredentialsAndStoredData = async function(...args) {
        await originalClearCredentialsAndStoredData(...args)
        console.log('Reloading on clearCredentialsAndStoredData')
        setTimeout(() => {
          window.location.reload()
        }, 2000)
      }

      return {
          Socket: window.require('WAWebSocketModel').Socket,
          // Socket: (await webpackRequireByMemberAttributeSync(['Socket'])).Socket,
          Stream: window.require('WAWebStreamModel').Stream,
          // Stream: (await webpackRequireByMemberAttributeSync(['StreamInfo'])).Stream,
          Cmd: window.require('WAWebCmd').Cmd,
          // Cmd: await findModuleSync(m => ((m.Cmd && m.CmdImpl) || (m.Cmd && m.default)) && (m.default || m.Cmd)),
          Conn: window.require('WAWebConnModel').Conn || {},
          Lid1X1MigrationUtils : window.require('WAWebLid1X1MigrationGating')?.Lid1X1MigrationUtils,
          full: false,
      }
  },
  { timeout: 1000 * 60 * 60, interval: 500, logFail: true },
)

export const full = () => pooling(
  async () => {
      return {
          // ...await webpackRequireByMemberAttributeSync(['Chat', 'Contact', 'Msg']),
          full: true,
          Chat: window.require('WAWebCollections').Chat,
          Contact: window.require('WAWebCollections').Contact,
          Msg: window.require('WAWebCollections').Msg,
          GroupMetadata: window.require('WAWebCollections').GroupMetadata,
          Socket: window.require('WAWebSocketModel').Socket,
          Stream: window.require('WAWebStreamModel').Stream,
          Cmd: window.require('WAWebCmd').Cmd,
          Conn: window.require('WAWebConnModel').Conn,
          MediaBlobCache: window.require('WAWebMediaInMemoryBlobCache').InMemoryMediaBlobCache,
          createIdModule: window.require('WAWebMsgKey'),
          createIdOriginal: window.require('WAWebMsgKey').newId,
          MsgKey: window.require('WAWebMsgKey'),
          // State: await findModule((module) => (module.STATE && module.STREAM ? module : null)),
          multiDeviceQueryExist: window.require('WAWebQueryExistsJob').queryExist,
          UserPrefs: window.require('WAWebUserPrefsMeUser'),
          Wid: window.require('WAWebWid'),
          MessageSender: window.require('WAWebSendTextMsgChatAction'),
          MessageSender2: window.require('WAWebSendMsgChatAction'),
          SeenSender: window.require('WAWebUpdateUnreadChatAction'),
          PresenceSender: window.require('WAWebPresenceChatAction'),
          ExitSender: window.require('WAWebExitGroupAction'),
          DeleteSender: window.require('WAWebDeleteChatAction'),
          PresenceCollection: window.require('WAWebPresenceCollection').PresenceCollection,
          MediaPrep: window.require('WAWebMedia'),
          MediaCollection: window.require('WAWebAttachMediaCollection'),
          MediaOpaqueData: window.require('WAWebMediaOpaqueData'),
          sendCreateGroup: window.require('WAWebGroupCreateJob').createGroup,
          GroupActionsUi: window.require('WAWebGroupsParticipantsApi'),
          GroupActions: window.require('WAWebGroupModifyParticipantsJob'),
          MessageActions: window.require('WAWebMsgActionCapability'),
          ParticipantModel: window.require('WAWebGroupParticipantModel'),
          ReactionActions: window.require('WAWebSendReactionMsgAction'),
          ReactionMessages: window.require('WAWebUpdateReactionMessageUIAction'),
          ReactionUtils: window.require('WAWebReactionsBEUtils'),
          ReactionDataUtils: window.require('WAWebReactionDataUtils'),
          SchemaReactions: window.require('WAWebSchemaReactions'),
          RevokeSender: window.require('WAWebChatSendMessages'),
          MessageLoader: window.require('WAWebChatLoadMessages'),
          ProfilePic: window.require('WAWebContactProfilePicThumbBridge'),
          SearchContext: window.require('WAWebChatMessageSearch'),
          ChatStore: window.require('WAWebForwardMessagesToChat'),
          ConfigStore: window.require('WAWebMediaGatingUtils'),
          WaveformUtils: window.require('WAWebWaveformUtils'),
          FileUtils: window.require('WAWebFileUtils'),
          FindChat: window.require('WAWebFindChatAction'),
          WidFactory: window.require('WAWebWidFactory'),
          GroupInviteAction: window.require('WAWebGroupInviteAction'),
          GroupInfo: window.require('WAWebSetPropertyGroupAction'),
          GroupDescription: window.require('WAWebGroupModifyInfoJob'),
          VCardUtils: window.require('WAWebFrontendVcardUtils'),
          GetEphemeralFieldsMsgActionsUtils: window.require("WAWebGetEphemeralFieldsMsgActionsUtils"),
          MessageEditAction: window.require("WAWebSendMessageEditAction"),
          SchemaMessage: window.require('WAWebSchemaMessage'),
          ApiChat: window.require('WAWebApiChat'),
          ChatGetExistingBridge: window.require('WAWebChatGetExistingBridge'),
          CreateChat: window.require('WAWebCreateChat'),
          ApiContact: window.require('WAWebApiContact'),
          ContactCollection: window.require('WAWebContactCollection'),
          LidMigrationUtils : window.require('WAWebLidMigrationUtils'),
          Lid1X1MigrationUtils : window.require('WAWebLid1X1MigrationGating')?.Lid1X1MigrationUtils,
      }
  },
  { timeout: 1000 * 60 * 60, interval: 500, logFail: true },
)
