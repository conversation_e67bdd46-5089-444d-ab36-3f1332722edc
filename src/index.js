import semverLt from 'semver/functions/lt'
import modules from './modules'
import { setStore } from './modules/store'
import * as FindStoreHaste from './findStoreHaste'
import polling from './modules/utils/polling'
import { setEventOnReaction } from './modules/reactions'
import { isOnChatPage } from './modules/status'
import EventEmitter from 'eventemitter3'
import {wait} from "./modules/helpers"
import applyMessagesPatch from "./patches/messagesPatch"

window.__EventEmitter = EventEmitter

const isFullyLoaded = () => {
  return !!(
    document.querySelector('.landing-main') // QR?
    || document.querySelector('#side') // QR?
    || document.querySelector('div[data-ref] button') // QR?
    || document.querySelector('div[data-ref] div') // QR?
    || document.querySelector('#main') // chat
    || document.querySelector('div[aria-label="Conectando ao WhatsApp"]') // SYNCING
  )
}

const LONG_TIMEOUT = 1000 * 60 * 60 * 24 * 365

const init = async () => {
  window.__fidelizza__ = {
    ...modules,
    isFullyLoaded,
    minVersion: '2.2350.1',
    env: process.env.NODE_ENV,
    srcVersion: process.env.SRC_VERSION,
  }

  console.log(`Running with env "${window.__fidelizza__.env}" and srcVersion "${process.env.SRC_VERSION}".`)

  console.log('Waiting for the loading to finish...')

  await polling(async () => isFullyLoaded(), { timeout: LONG_TIMEOUT, interval: 500 })

  console.log('Page loading finished, starting initial store load...')

  console.log(`Current running WhatsApp version is "${modules.status.getVersion()}".`)

  if (semverLt(modules.status.getVersion(), window.__fidelizza__.minVersion)) {
    console.warn(
      `Current WhatsApp Web version is not compatible with running version of whatsapp-scripts-src (minVersion: ${window.__fidelizza__.minVersion}).`
    )
  }

  console.log('Waiting 5000ms')

  await wait(5 * 1000)

  console.log('Loading initial store...')
  await FindStoreHaste.initial().then(setStore)

  console.log('Initial store loaded. Waiting for mode MAIN...')
  console.log('Current mode:', modules.status.getMode?.() || 'unknown')

  await polling(async () => isOnChatPage(), { timeout: LONG_TIMEOUT, interval: 500 })

  console.log('On mode MAIN. Starting full store load...')

  await FindStoreHaste.full().then((store) => {
    console.log('Full store loaded with modules:', Object.keys(store))
    console.log('Store.Msg available:', !!store.Msg)
    console.log('Store.Msg type:', typeof store.Msg)
    if (store.Msg) {
      console.log('Store.Msg._models length:', store.Msg._models?.length || 0)
      console.log('Store.Msg.on available:', typeof store.Msg.on)
    }
    setStore(store)
  })

  console.log('Full store loaded.')

  setEventOnReaction()

  applyMessagesPatch()

  console.log('Set event on reaction and WhatsApp wrapper. Init finished.')
  console.log('Final Store.Msg check:', !!modules.store.getStore()?.Msg)

  // Test raw Store.Msg events
  console.log('Setting up raw Store.Msg event listener for debugging...')
  try {
    const store = modules.store.getStore()
    let rawEventCount = 0
    store.Msg.on('add', (message) => {
      rawEventCount++
      console.log(`🔥 RAW Store.Msg 'add' event #${rawEventCount}:`, {
        id: message?.id?._serialized,
        type: message?.type,
        from: message?.from?._serialized,
        fromUser: message?.from?.user,
        isNewMsg: message?.isNewMsg,
        timestamp: new Date().toISOString()
      })
    })
    console.log('✅ Raw Store.Msg event listener configured')
  } catch (error) {
    console.error('❌ Error setting up raw Store.Msg listener:', error)
  }

  // Test onMessage setup
  console.log('Testing onMessage setup...')
  try {
    let testMessageCount = 0
    modules.messages.onMessage((message) => {
      testMessageCount++
      console.log(`🔔 TEST onMessage callback triggered #${testMessageCount} for message:`, {
        id: message?.id?._serialized,
        type: message?.type,
        from: message?.from?._serialized,
        isNewMsg: message?.isNewMsg
      })
    })
    console.log('✅ onMessage test listener configured successfully')
  } catch (error) {
    console.error('❌ Error setting up onMessage test listener:', error)
  }
}

init().catch(console.error)
