import { isConflicted } from './status'
import getStore from './store'

/**
 * @return {boolean}
 */
export function takeover() {
  if (!isConflicted()) return false

  document.querySelectorAll('div[role="button"]')[1].click()
  return true
}

/**
 * @return {boolean}
 */
export function checkRememberMe() {
  const el = document.querySelector('input[name=rememberMe]')

  if (!el.checked) {
    el.click()
  }

  return true
}

/**
 * @return {void}
 */
export function renewQrCode() {
  const el =
    document.querySelector('div[data-ref] button') ||
    document.querySelector('div[data-ref] div')
  if (el) el.click()
}

/**
 * @return {Promise<*>}
 */
export function logout() {
  return getStore().Socket.logout()
}
