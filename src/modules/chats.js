import memoize from './utils/memoize'
import { createFile, createFileFromBase64, prepareMessage, wait } from './helpers'
import {
  objectIdToStringId,
  transformContact,
  getWAContactById,
  transformContactOmitFalsy,
  getValidId,
  stringIdToObjectId,
  getContactById,
  getLidById,
  getPhoneNumberById,
} from './contacts'
import {
  compareMessageId,
  getWAMessageById,
  getWAMessageByIdAsync,
  transformMessage,
  transformMessageWithContactOmitFalsy,
  getMessageById,
  getChatFromMessage,
} from './messages'
import { getStore } from './store'
import { getNewMessageId, getNewId, createWid } from './helpers'
import queuedAsyncMap from './utils/queuedAsyncMap'
import polling from './utils/polling'
import getFileNameFromResponse from './utils/getFileNameFromResponse'
import { compare } from 'compare-versions'
import { isLidMigrated } from './status'

/**
 * @typedef {{
 * groupMetadata: { name: string, creation: number }
 * }} WAChat
 */

/**
 * @typedef {{
 * id: string,
 * name: string,
 * totalParticipants: number,
 * }} Group
 */

/**
 * @typedef {{
 * id: string,
 * name: string,
 * }} Chat
 */


/**
 * @return {WAChat[]}
 */
export function getWAChats() {
  return getStore().Chat._models.map((chat) => {
      chat.isGroup = chat.id.isGroup()
      return chat
  })
}

/**
 * @param id {string}
 * @return {WAChat|null}
 */
export function getWAChatById(id) {
  if(!id) return null
  const chatId = id?.id ? objectIdToStringId(id) : id
  const chats = getWAChats()

  for (let i = 0; i <= chats.length - 1; i += 1) {
    if (objectIdToStringId(chats[i].id) === chatId) {
      return chats[i]
    }
  }
  return null
}

/**
 * @param contactId {string|Wid}
 * @return {Promise<WAChat|null>}
 */
export async function findOrCreateChat(contactId, retry = false) {
  const chatId = typeof contactId == 'string' ? stringIdToObjectId(contactId) : contactId
  try {
    const {chat, created} = await getStore().FindChat.findOrCreateLatestChat(chatId).catch((error) => {
      if(retry){
        throw error
      }
      return {}
    })
   
    if(!chat){
      console.log(`[findOrCreateChat] Chat not found, retrying: ${contactId}`)

      if(!(await getValidId(chatId?.toString()))){
        console.warn(`[findOrCreateChat] invalid chat id: ${rawId}`)
      }

      return findOrCreateChat(chatId, true)
    }

    if(created){
      console.log(`[findOrCreateChat] Chat created: ${contactId}`)
    }
    chat.isGroup = chat.id.isGroup()

    return chat

  } catch (error) {
    console.error(`[findOrCreateChat] Error creating chat: ${chatId}`, error)
  }
  return null
}

/**
 * @param id {string}
 * @return {Promise<WAChat|null>}
 */
export async function getWAChatByIdOrAdd(id) {
  if(!id) return null
  const chatId = id?.id ? objectIdToStringId(id) : id
  
  try{
    let chat = !isLidMigrated() && getWAChatById(getPhoneNumberById(id))

    if(!chat){
      chat = getWAChatById(getLidById(id))
      if(!chat || (isLidMigrated() && !chat?.accountLid)){
        chat = await findOrCreateChat(id)
      }
    }
    
    if(!chat){
      throw new Error(`Chat not found to id ${id}`)
    }

    return chat
  }catch(error){
    console.error(error)
    return null
  }
}

/**
 * @param groupId {string}
 * @param value {boolean}
 * @return {string|null}
 * @description Method responsible for applying group message sending permission
 */
export async function onlyAdminCanSendMsg(groupId, value) {
  const groupChat = getWAChatById(groupId)

  if (!groupChat) {
    console.error('Unable to apply permission. Group not found!', groupId)
    return false
  }

  const status = value === true ? 1 : 0
  await getStore().GroupInfo.setGroupProperty(groupChat, 'announcement', status)
  console.log('permission applied successfully', groupId)

  return true
}

/**
 * @param groupId {string}
 * @param value {0 | 86400[24h] | 604800[7d] | 7776000[90d]}
 * @return {string|null}
 * @description Method responsible for applying tempory message
 */
export async function applyTemporaryMessage(groupId, value) {
  const groupChat = getWAChatById(groupId)

  if (!groupChat) {
    console.error('Unable to apply permission. Group not found!', groupId)
    return false
  }

  const period = !value ? 0 : value
  await getStore().GroupInfo.setGroupProperty(groupChat, 'ephemeral', period)
  console.log('permission applied successfully', groupId)

  return true
}

/**
 * @param groupId {string}
 * @param value {boolean}
 * @return {boolean}
 * @description Method responsible for set if members can add new members
 */
export async function setMemberAddMode(groupId, value) {
  const groupChat = getWAChatById(groupId)

  if (!groupChat) {
    console.error('Unable to apply permission. Group not found!', groupId)
    return false
  }

  const status = value === true ? 1 : 0
  await getStore().GroupInfo.setGroupProperty(groupChat, 'member_add_mode', status)
  console.log('permission applied successfully', groupId)

  return true
}

/**
 * @param groupId {string}
 * @param value {boolean}
 * @return {boolean}
 * @description Method responsible for defining whether the administrator needs to approve new members
 */
export async function setAdminApproval(groupId, value) {
  const groupChat = getWAChatById(groupId)

  if (!groupChat) {
    console.error('Unable to apply permission. Group not found!', groupId)
    return false
  }

  const status = value === true ? 1 : 0
  await getStore().GroupInfo.setGroupProperty(groupChat, 'membership_approval_mode', status)
  console.log('permission applied successfully', groupId)

  return true
}

/**
 * @param groupId {string}
 * @param value {boolean}
 * @return {boolean}
 * @description Method responsible for set if only admins can edit group properties
 */
export async function onlyAdminCanEditProperties(groupId, value) {
  const groupChat = getWAChatById(groupId)

  if (!groupChat) {
    console.error('Unable to apply permission. Group not found!', groupId)
    return false
  }

  const status = value === true ? 1 : 0
  await getStore().GroupInfo.setGroupProperty(groupChat, 'restrict', status)
  console.log('permission applied successfully', groupId)

  return true
}

/**
 * @param groupId {string}
 * @param description {string}
 * @return {boolean}
 * @description Method responsible to set group description
 */
export async function setGroupDescription(groupId, description) {
  const groupChat = getWAChatById(groupId)

  if (!groupChat) {
    console.error('Unable to get chat. Group not found!', groupId)
    return false
  }

  try {
    const groupMetadata = await getStore().GroupMetadata.get(groupId)

    const newTag = await getStore().createIdOriginal()

    if (!groupMetadata.descId) {
      await getStore().GroupDescription.setGroupDescription(groupMetadata.id, description, newTag)
      console.log('Description applied successfully', groupId)
      return true
    }

    if (!groupMetadata.id) {
      console.error('Unable to apply description', groupId)
      return false
    }

    await getStore().GroupDescription.setGroupDescription(groupMetadata.id, description, newTag, groupMetadata.descId)
    console.log('Description applied successfully', groupId)

    return true
  } catch (error) {
    console.error('Unable to apply description', groupId)
    return false
  }
}

/**
 * @param groupId {string}
 * @return {object | boolean}
 * @description Method responsible to get all group configs
 */
export async function getGroupConfig(groupId) {
  const groupChat = getWAChatById(groupId)

  if (!groupChat) {
    console.error('Unable to get chat. Group not found!', groupId)
    return false
  }

  try {
    const inviteCode = await getStore().GroupInviteAction.queryGroupInviteCode({
      groupInviteCodePromise: async()=> ({code:0}), 
      ...groupChat.id
    }, 1)

    const groupMetadata = await getStore().GroupMetadata.get(groupId)

    return formatGroupConfig(inviteCode, groupMetadata)
  } catch (error) {
    console.error('Unable to get group configs', groupId)
    return false
  }
}

/**
 * @param id {string}
 * @return {Promise<Contact[]|null>}
 */
export async function getGroupParticipants(id) {
  const chat = await getWAChatByIdOrAdd(id)
  if (!chat) return null

  return chat.groupMetadata.participants._models
    .map(m => {
      const contact = m.contact ? m.contact : m

      contact.isAdmin = m.__x_isAdmin
      contact.isSuperAdmin = m.__x_isSuperAdmin

      return contact
    }) // retro compatibility added at 2.2106.5
    .map(transformContact)
}

export function createMessageId() {
  return getStore().createIdOriginal()
}

export function onMessageSent(chat, cb, overrideMessageId) {
  if (typeof overrideMessageId === 'string' && (overrideMessageId.length < 16 || overrideMessageId.includes('_'))) {
    throw new Error('Invalid overrideMessageId')
  }

  const methodName = getStore().createIdModule.newId ? 'newId' : 'newTag'

  let off

  getStore().createIdModule[methodName] = async (...props) => {
    const messageId = overrideMessageId || await getStore().createIdOriginal(...props)

    const messageListener = (m) => {
      if (!compareMessageId(m, messageId)) return

      cb(m)

      chat.msgs.off('add', messageListener)
    }
    chat.msgs.on('add', messageListener)

    off = () => chat.msgs.off('add', messageListener)

    getStore().createIdModule[methodName] = getStore().createIdOriginal

    return messageId
  }

  return () => off && off()
}

/**
 * @param id {string}
 * @param text {string}
 * @param quotedMessageId {string}
 * @param overrideMessageId {string}
 * @return {*}
 */
export async function sendMessageToId(id, text, quotedMessageId, overrideMessageId) {

  const chat = await getWAChatByIdOrAdd(id)

  const Sender = getStore().MessageSender

  const quotedMsg = quotedMessageId && await getWAMessageByIdAsync(quotedMessageId)

  // help to prevent bans
  const SeenSender = getStore().SeenSender
  await SeenSender.sendSeen(chat, false).catch(() => null)

  // WARNING: do not put anything async from here until sendTextMsgToChat
  const messagePromise = new Promise((resolve) => {
    onMessageSent(chat, resolve, overrideMessageId)
  })

  Sender.sendTextMsgToChat(chat, text, { quotedMsg }).catch(console.error)

  const m = await messagePromise
  await wait(1) // needed so quotedMessage is loaded
  return m
}

/**
 * @param chat {WAChat}
 * @param body { text: string }
 * @param mentionedList {Array}
 * @return {string[]|null}
 */
export function getMentionedJidList(chat, body, mentionedList = []) {
  const participants = chat?.groupMetadata?.participants?._models || []

  if (!participants.length || !body?.text) return null

  /**
   * Try to detect mentioned list from text
   */
  if (!mentionedList.length) {
    const ids = body.text?.match(/(?<=@)(\d+)/g) || [];
    const jids = ids.map((id) => getPhoneNumberById(`${id}@c.us`)).filter(Boolean)
    const lids = ids.map((id) => getLidById(`${id}@lid`)).filter(Boolean)
    mentionedList = [...jids, ...lids]
  }

  const mentionedJidList = []
  mentionedList.forEach(id => {
    const jidId = participants.find(p => p.id?.toString() === id 
      || p.id?.toString() === getPhoneNumberById(id) 
      || p.id?.toString() === getLidById(id)
    )
    if(jidId?.id?.toString() !== id){
      body.text = body.text?.replaceAll(`@${stringIdToObjectId(id)?.user}`, `@${jidId?.id?.user}`)
    }
    mentionedJidList.push(jidId?.id)
  })

  return mentionedJidList.filter(Boolean)?.length ? mentionedJidList.filter(Boolean) : null
}

/**
 * @param chatId {string}
 * @param text {string}
 * @param options {Object}
 * @param options.mentionedList {Array}
 * @param options.quotedMessageId {string}
 * @return {*}
 */
export async function sendMentionMessageToId(chatId, text, options) {
  const chat = await getWAChatByIdOrAdd(chatId)
  
  if (!chat) return null
  const body = { text }
  const { mentionedList, quotedMessageId } = options || {}
  const quotedMsg = quotedMessageId && await getWAMessageByIdAsync(quotedMessageId)
  const mentionedJidList = getMentionedJidList(chat, body, mentionedList)

  if (!mentionedJidList) return null

  const preparedMessage = await prepareMessage(chat, {
    body: body.text,
    type: "chat",
    mentionedJidList,
    ...(quotedMsg && {
      ...quotedMsg.msgContextInfo(chat.id)
    })
  })

  const Sender = getStore().MessageSender2
  // help to prevent bans
  const SeenSender = getStore().SeenSender
  await SeenSender.sendSeen(chat, false).catch(() => null)

  if (!preparedMessage) return null

  const response = await Sender.addAndSendMsgToChat(chat, preparedMessage)

  return response[0]
}

/**
 * @param msgId {string}
 * @param text {string}
 * @param options {Object}
 * @return {*}
 */
export async function editMessage(msgId, text, options) {
  const msg = getMessageById(msgId);
  if (!msg) throw new Error(`Message not found: ${msgId}`)

  const chat = await getChatFromMessage(msg)
  if (!chat) throw new Error(`Chat not found.`)

  const MessageActions = getStore().MessageActions

  //It is only possible to change messages sent by the same device
  if (!MessageActions.canEditText(msg) && !MessageActions.canEditCaption(msg)) {
    throw new Error(`Cannot edit message: ${msgId}.`)
  }

  let mentionedJidList = null
  const body = { text }
  const makeMentionedJidList = getMentionedJidList(chat, body, options?.mentionedList)
  if (makeMentionedJidList) {
    mentionedJidList = makeMentionedJidList
  }

  let preparedMessage = await prepareMessage(chat, {
    type: 'protocol',
    subtype: 'message_edit',
    protocolMessageKey: msg.id,
    body: body.text,
    caption: body.text,
    editMsgType: msg.type,
    ...(mentionedJidList && { mentionedJidList }),
    ...options,
  })

  if (!preparedMessage) throw new Error(`Cannot prepare message for editing: ${msgId}.`)

  preparedMessage.latestEditMsgKey = preparedMessage.id;
  preparedMessage.latestEditSenderTimestampMs = preparedMessage.t;

  return getStore().MessageEditAction.addAndSendMessageEdit(msg, preparedMessage)
}


/**
 * Prepare waveform form message audio file
 *
 * @category Message
 * @internal
 */
export async function prepareAudioWaveform(
  file
) {
  /**
   * @see https://css-tricks.com/making-an-audio-waveform-visualizer-with-vanilla-javascript/
   */
  try {
    const audioData = await file.arrayBuffer();
    const audioContext = new AudioContext();
    const audioBuffer = await audioContext.decodeAudioData(audioData);

    const rawData = audioBuffer.getChannelData(0); // We only need to work with one channel of data
    const samples = 64; // Number of samples we want to have in our final data set
    const blockSize = Math.floor(rawData.length / samples); // the number of samples in each subdivision
    const filteredData = [];
    for (let i = 0; i < samples; i++) {
      const blockStart = blockSize * i; // the location of the first sample in the block
      let sum = 0;
      for (let j = 0; j < blockSize; j++) {
        sum = sum + Math.abs(rawData[blockStart + j]); // find the sum of all the samples in the block
      }
      filteredData.push(sum / blockSize); // divide the sum by the block size to get the average
    }

    // This guarantees that the largest data point will be set to 1, and the rest of the data will scale proportionally.
    const multiplier = Math.pow(Math.max(...filteredData), -1);
    const normalizedData = filteredData.map((n) => n * multiplier);

    // Generate waveform like WhatsApp
    const waveform = new Uint8Array(
      normalizedData.map((n) => Math.floor(100 * n))
    );

    return {
      duration: Math.floor(audioBuffer.duration),
      waveform,
    };
  } catch (error) {
    console.error('Failed to generate waveform', error);
    return null
  }
}

/**
 * @param chat {WAChat}
 * @param file {File}
 * @param caption {string}
 * @param isPtt {boolean}
 * @param quotedMessageId {string}
 * @param overrideMessageId {string}
 * @param options {Object}
 * @param options.asDocument {boolean}
 * @param options.asSticker {boolean}
 * @param options.asGif {boolean}
 * @param options.mentionedList {array}
 * @return {Promise<*>}
 */
export async function sendMediaToChat(chat, file, caption, isPtt, quotedMessageId, overrideMessageId, options = {}) {
  const { asDocument, asSticker, asGif, resend, mentionedList } = options
  const MediaOpaqueData = getStore().MediaOpaqueData

  const isAudio = file.type.startsWith('audio/')

  let media
  try {
    const MediaPrep = getStore().MediaPrep

    const rawMedia = await MediaOpaqueData.createFromData(file, file.type)

    const rawMediaOptions = {
      isPtt,
      asDocument,
      asSticker,
      asGif,
    }

    if (isPtt) {
      const waveformFields = await prepareAudioWaveform(file)

      if (waveformFields) {
        rawMediaOptions.precomputedFields = waveformFields
      }
    }

    media = await MediaPrep.prepRawMedia(rawMedia, rawMediaOptions)

    await Promise.race([
      media.waitForPrep(),
      wait(15 * 1000),
    ])
  } catch (e) {
    console.error('media prep failed', e, overrideMessageId, file)
    throw e
  }
  let offMessageSent

  // help to prevent bans
  const SeenSender = getStore().SeenSender
  await SeenSender.sendSeen(chat, false).catch(() => null)

  // !!!!!!!!! WARNING: do not put anything async from here until sendToChat !!!!!!!!!!!
  const messagePromise = new Promise((resolve) => {
    offMessageSent = onMessageSent(chat, resolve, overrideMessageId)
  })
  const body = { text: caption }
  const quotedMessage = getWAMessageById(quotedMessageId)
  const mentionedJidList = getMentionedJidList(chat, body, mentionedList)

  let result
  try {
    result = await media.sendToChat(chat, {
      caption: !isAudio ? body.caption : undefined,
      quotedMsg: quotedMessage,
    ...(mentionedJidList && { mentionedJidList }),
    })
  } catch (error) {
    console.error('send media to chat failed', error, overrideMessageId, file)
    offMessageSent()
    throw error
  }

  const resultMessage = result?.messageSendResult

  // retry if error
  if (resultMessage === 'ERROR_UNKNOWN' && media?._mediaData?.mediaStage === 'ERROR_UNSUPPORTED' && !resend) {
    console.warn('Error sending media, retrying as document', [
      result,
      media?._mediaData?.mediaStage,
      chat,
      file,
      caption,
      isPtt,
      quotedMessageId,
      overrideMessageId,
      options
    ])

    if (file.name === 'null') {
      const name = file.type.split('/')[0]
      file = new File([file], name, { type: file.type })
    }

    offMessageSent()
    return sendMediaToChat(chat, file, caption, isPtt, quotedMessageId, overrideMessageId, {
      ...options,
      asDocument: true,
      resend: true
    })
  }

  if (resultMessage !== 'OK') {
    offMessageSent()
    throw new Error(resultMessage)
  }

  const m = await messagePromise
  await wait(1)
  return m
}

/**
 * @param chat {WAChat}
 * @param file {File}
 * @param caption {string}
 * @param isPtt {boolean}
 * @param quotedMessageId {string}
 * @param overrideMessageId {string}
 * @return {Promise<*>}
 */
export function sendMediaToChat2(chat, file, caption, isPtt, quotedMessageId, overrideMessageId) {
  const MediaCollection = getStore().MediaCollection
  const mc = new MediaCollection(chat)

  return mc.processAttachments([{ file }, 1], chat, 1).then(() => {
    return new Promise((resolve) => {
      const media = mc._models[0]

      // WARNING: do not put anything async from here until sendToChat
      onMessageSent(chat, resolve, overrideMessageId)

      const quotedMessage = getWAMessageById(quotedMessageId)

      media.sendToChat(chat, {
        caption,
        quotedMsg: quotedMessage,
      })
    })
  })
}

/**
 * @param id {string}
 * @param file {File}
 * @param caption {string}
 * @param isPtt {boolean}
 * @param quotedMessageId {string}
 * @param overrideMessageId {string}
 * @param options {Object}
 * @param options.asDocument {boolean}
 * @param options.asSticker {boolean}
 * @param options.mentionedList {array}
 * @return {Promise<*>}
 */
export async function sendMediaToId(id, file, caption, isPtt, quotedMessageId, overrideMessageId, options) {
  const chat = await getWAChatByIdOrAdd(id)
  return sendMediaToChat(chat, file, caption, isPtt, quotedMessageId, overrideMessageId, options)
}

/**
 * @param id {string}
 * @param base64 {string}
 * @param name {string}
 * @param mimetype {string}
 * @param caption {string}
 * @param isPtt {boolean}
 * @param quotedMessageId {string}
 * @param overrideMessageId {string}
 * @param options {Object}
 * @param options.asDocument {boolean}
 * @param options.asSticker {boolean}
 * @return {Promise<*>}
 */
export function sendBase64MediaToId(id, base64, name, mimetype, caption, isPtt, quotedMessageId, overrideMessageId, options) {
  const file = createFileFromBase64(base64, name, mimetype)
  return sendMediaToId(id, file, caption, isPtt, quotedMessageId, overrideMessageId, options)
}

/**
 * @param id {string}
 * @return {Promise<Blob>}
 */
export const fetchMediaAsBlob = memoize({ maxSize: 10, maxAge: 6 * 60 * 60 * 1000, isPromise: true })(
  (url) => fetch(url)
    .then(async (response) => {
      const blob = await response.blob()
      const fileName = getFileNameFromResponse(response)
      return { blob, fileName }
    })
)

/**
 * @param id {string}
 * @param url {string}
 * @param name {string}
 * @param mimetype {string}
 * @param caption {string}
 * @param isPtt {boolean}
 * @param quotedMessageId {string}
 * @param overrideMessageId {string}
 * @param options {Object}
 * @param options.asDocument {boolean}
 * @param options.asSticker {boolean}
 * @param options.mentionedList {array}
 * @return {Promise<*>}
 */

export function sendUrlMediaToId(id, url, name, mimetype, caption, isPtt, quotedMessageId, overrideMessageId, options = {}) {
  return fetchMediaAsBlob(url)
    .then(async ({ blob, fileName }) => {

      const validTypes = ['audio', 'video', 'image', 'document'];
      const type = validTypes.includes(mimetype.split('/')[0]) ? mimetype.split('/')[0] : 'document';

      const uploadLimit = getStore().ConfigStore.getUploadLimit(type)
      const isMedia = !options.asDocument && ['audio', 'video', 'image'].some(m => type)

      if (blob.size > uploadLimit) {
        throw new Error(isMedia ? 'MEDIA_TOO_LARGE' : 'FILE_TOO_LARGE')
      }

      return { blob, fileName }
    })
    .then(({ blob, fileName }) => createFile(blob, name || fileName, mimetype))
    .then((file) => sendMediaToId(id, file, caption, isPtt, quotedMessageId, overrideMessageId, options))
}

/**
 * @param id {string}
 * @return {Promise<*>}
 */
export async function markReadById(id) {
  const SeenSender = getStore().SeenSender

  const chat = await getWAChatByIdOrAdd(id)
  return SeenSender.sendSeen(chat, false)
}

/**
 * @param id {string}
 * @return {Promise<*>}
 */
export async function markComposing(id) {
  const PresenceSender = getStore().PresenceSender
  const PresenceCollection = getStore().PresenceCollection

  const chat = await getWAChatByIdOrAdd(id)

  await PresenceCollection.find(chat.id)

  return PresenceSender.markComposing(chat)
}

/**
 * @param chat {WAChat}
 * @return {Group}
 */
export function formatGroup(chat) {
  return {
    id: objectIdToStringId(chat.id),
    name: chat.formattedTitle,
    totalParticipants: chat.isGroup? chat.groupMetadata.participants._models.length : 0
  }
}

export function formatGroupConfig(inviteCode, metadata) {
  return {
    description: metadata.desc,
    inviteUrl: "https://chat.whatsapp.com/" + inviteCode.code || '',
    onlyAdminCanSend: metadata.announce,
    onlyAdminCanEditProperties: metadata.restrict,
    temporaryMessage: metadata.ephemeralDuration || 0,
    memberAddMode: metadata.memberAddMode === 'admin_add' ? false : true,
    membershipApprovalMode: metadata.membershipApprovalMode
  }
}

export function uploadLimits(waType) {
  const validTypes = ['audio', 'video', 'image', 'document'];
  const type = validTypes.some(m => m === waType || 'document');
  return getStore().ConfigStore.getUploadLimit(type)
}

/**
 * @return {WAChat[]}
 */
export function getAllWAChatGroups() {
  return getWAChats().filter(c => c.isGroup)
}

/**
 * @param id {string}
 * @return {Promise<WAChat>}
 */
export function fetchGroupParticipants(id) {
  return getStore().GroupMetadata.find(id)
}

/**
 * @return {Promise<WAChat>[]}
 */
export const fetchAllGroupParticipants = memoize.promise(async () => {
  const groups = await getAllWAChatGroups()
  return queuedAsyncMap(groups, chat => fetchGroupParticipants(chat.id), 25)
})

/**
 * @param id {string}
 * @return {Chat|null}
 */
export async function getChatById(id) {
  const chat = await getWAChatByIdOrAdd(id)
  return formatGroup(chat)
}

/**
 * @param id {string}
 * @return {Group}
 */
export async function getGroupById(id) {
  const chat = await getWAChatByIdOrAdd(id)
  return formatGroup(chat)
}


/**
 * @return {Group[]}
 */
export function getAllGroups() {
  const chats = getAllWAChatGroups()
  return chats.map(formatGroup)
}

/**
 * @return {Group[]}
 */
export function getAllBroadcasts() {
  const chats = getWAChats().filter(chat => chat.isBroadcast)
  return chats.map(formatGroup)
}

/**
 * @param chat {WAChat}
 * @returns {Promise<*>}
 */
export async function loadEarlierMessages(chat) {
  console.log(`[${new Date().toISOString()}] [internal call] loadEarlierMessages`, objectIdToStringId(chat.contact.id))
 
  //@TODO: Montar lista baseada no idioma da ui do whatsapp
  const loadMoreTexts = [
    "Clique neste aviso para carregar mensagens mais antigas do seu celular.",
    "Não foi possível carregar as mensagens mais antigas. Abra o WhatsApp no seu celular e clique neste aviso para tentar novamente."
  ];

  let loadMoreButton = null;

  for (const text of loadMoreTexts) {
    loadMoreButton = document.evaluate(
      `//button[.//div/div[text()='${text}']]`,
      document,
      null,
      XPathResult.FIRST_ORDERED_NODE_TYPE,
      null
    ).singleNodeValue;

    if (loadMoreButton) break;
  }

  if (loadMoreButton) {
    loadMoreButton.click();
    await wait(2000)
  } else {
    console.log(
      `[${new Date().toISOString()}] [internal call] loadEarlierMessages - No more messages to load or button not found.`
    );
  }
  const MessageLoader = getStore().MessageLoader

  return MessageLoader.loadEarlierMsgs(chat)
}

/**
 * @param id {string}
 * @returns {Promise<*>}
 */
export async function loadEarlierMessagesById(id) {
  const chat = await getWAChatByIdOrAdd(id)
  return loadEarlierMessages(chat)
}

/**
 * @param chat {WAChat}
 * @param timestamp {string}
 * @returns {Promise<*>}
 */
export async function loadEarlierMessagesTillDate(chat, timestamp) {
  if (!chat) {
    throw new Error('Chat is required.')
  }

  if ((timestamp && chat.msgs._models[0] && chat.msgs._models[0].t < timestamp) || chat.msgs.msgLoadState.noEarlierMsgs) {
    return
  }

  const loaded = await loadEarlierMessages(chat)

  if (!loaded || !loaded.length) return

  await wait(15)

  return loadEarlierMessagesTillDate(chat, timestamp)
}

/**
 * @param id {string}
 * @param timestamp {string}
 * @returns {Promise<*>}
 */
export async function loadEarlierMessagesTillDateById(id, timestamp) {
  const chat = await getWAChatByIdOrAdd(id)
  return loadEarlierMessagesTillDate(chat, timestamp)
}

export async function getChatWithMessagesTillDate(timestamp) {
  if (!timestamp) {
    throw new Error('timestamp is required')
  }

  const chats = await getWAChats()

  const formattedContacts = []

  for (const chat of chats) {
    const formattedContact = transformContact(chat.contact)
    formattedContact.messages = []

    await loadEarlierMessagesTillDate(chat, timestamp)

    for (const message of chat.msgs._models) {
      if (message.t > timestamp) {
        const formattedMessage = await transformMessage(message)
        formattedContact.messages.push(formattedMessage)
      }
    }

    formattedContacts.push(formattedContact)
  }

  return formattedContacts
}


/**
 * @param name {string}
 * @param contactIds {string[]}
 * @returns {Promise<*>}
 */
export function createGroup(name, contactIds) {
  const creationData = compare(self.Debug.VERSION, '2.3000.1014489107', '>=') ? {
    title: name,
    ephemeralDuration: 0,
    restrict: true,
    announce: true,
    membershipApprovalMode: false,
    memberAddMode: true,
    addressingModeOverride: "lid",
  } : name

  return getStore().sendCreateGroup(creationData, contactIds.map(id => {
    if(id?.endsWith("@lid")){
      return { lid: stringIdToObjectId(id) }
    }
    return { phoneNumber: stringIdToObjectId(id) }
  }), 0)
}

/**
 * @param id {string}
 * @return {Promise<*>}
 */
export async function leaveGroupById(id) {
  const ExitSender = getStore().ExitSender

  const chat = getWAChatById(id)

  if (!chat) {
    throw new Error(`Chat with id "${id}" not found.`)
  }

  await ExitSender.sendExitGroup(chat)

  await polling(
    async () => !getWAChatById(id).groupMetadata.participants.iAmMember(),
    { timeout: 60 * 1000, interval: 100 },
  ).catch(() => {
    throw new Error('Timeout out waiting to leave group.')
  })

  await wait(500)

  return true
}

/**
 * Add participant of Group
 * @param {*} idGroup '<EMAIL>'
 * @param {*} idParticipant '<EMAIL>'
 * @param {*} done - function - Callback function to be called when a new message arrives.
 */
export function addParticipantGroup(idGroup, idParticipant, done) {
  getStore().Wap.addParticipants(idGroup, [idParticipant]).then(() => {
    const metaDataGroup = getStore().GroupMetadata.get(idGroup)
    let checkParticipant = metaDataGroup.participants._index[idParticipant];
    if (checkParticipant === undefined) {
      done(true); return true;
    }
  })
}

/**
 * Remove participant of Group
 * @param {*} idGroup '<EMAIL>'
 * @param {*} idParticipant '<EMAIL>'
 * @param {*} done - function - Callback function to be called when a new message arrives.
 */
export function removeParticipantGroup(idGroup, idParticipant, done) {
  getStore().Wap.removeParticipants(idGroup, [idParticipant]).then(() => {
    const metaDataGroup = getStore().GroupMetadata.get(idGroup)
    let checkParticipant = metaDataGroup.participants._index[idParticipant];
    if (checkParticipant === undefined) {
      done(true); return true;
    }
  })
}

/**
 * Promote Participant to Admin in Group
 * @param {*} idGroup '<EMAIL>'
 * @param {*} idParticipant '<EMAIL>'
 * @param {*} done - function - Callback function to be called when a new message arrives.
 */
export function promoteParticipantAdminGroup(idGroup, idParticipant, done) {
  getStore().Wap.promoteParticipants(idGroup, [idParticipant]).then(() => {
    const metaDataGroup = getStore().GroupMetadata.get(idGroup)
    let checkParticipant = metaDataGroup.participants._index[idParticipant];
    if (checkParticipant !== undefined && checkParticipant.isAdmin) {
      done(true); return true;
    }
    done(false); return false;
  })
}

/**
 * Demote Admin of Group
 * @param {*} idGroup '<EMAIL>'
 * @param {*} idParticipant '<EMAIL>'
 * @param {*} done - function - Callback function to be called when a new message arrives.
 */
export function demoteParticipantAdminGroup(idGroup, idParticipant, done) {
  getStore().Wap.demoteParticipants(idGroup, [idParticipant]).then(() => {
    const metaDataGroup = getStore().GroupMetadata.get(idGroup)
    if (metaDataGroup === undefined) {
      done(false); return false;
    }
    let checkParticipant = metaDataGroup.participants._index[idParticipant];
    if (checkParticipant !== undefined && checkParticipant.isAdmin) {
      done(false); return false;
    }
    done(true); return true;
  })
}

/**
 * @param id {string}
 * @return {Promise<*>}
 */
export async function deleteChatById(id) {
  const DeleteSender = getStore().DeleteSender

  const chat = getWAChatById(id)

  if (!chat) {
    throw new Error(`Chat with id "${id}" not found.`)
  }

  await DeleteSender.sendDelete(chat)

  await polling(
    async () => !getWAChatById(id),
    { timeout: 60 * 1000, interval: 100 },
  ).catch(() => {
    throw new Error('Timeout out waiting for chat to be deleted.')
  })

  return true
}

/**
 * Send contact card to a specific chat using the chat ids
 *
 * @param {string} id '<EMAIL>'
 * @param {string|array} contact '<EMAIL>' | ['<EMAIL>', '<EMAIL>, ... '<EMAIL>']
 */
export function sendContact(id, contact) {
  if (!Array.isArray(contact)) {
    contact = [contact];
  }

  contact = contact.map((c) => {
    return getWAContactById(c);
  });

  if (contact.length > 1) {
    getWAChatById(id).sendContactList(contact);
  } else if (contact.length === 1) {
    getWAChatById(id).sendContact(contact[0]);
  }
}

/**
 * Send Customized VCard without the necessity of contact be a Whatsapp Contact
 *
 * @param {string} id '<EMAIL>'
 * @param {object|array} vcard { displayName: 'Contact Name', vcard: 'BEGIN:VCARD\nVERSION:3.0\nN:;Contact Name;;;\nEND:VCARD' } | [{ displayName: 'Contact Name 1', vcard: 'BEGIN:VCARD\nVERSION:3.0\nN:;Contact Name 1;;;\nEND:VCARD' }, { displayName: 'Contact Name 2', vcard: 'BEGIN:VCARD\nVERSION:3.0\nN:;Contact Name 2;;;\nEND:VCARD' }]
 */
export function sendVCardOld(id, vcard) {
  var tempMsg = Object.create(getStore().Msg._models.filter(msg => msg.__x_isSentByMe)[0]);

  var newId = getNewMessageId(id);

  var extend = {
    ack: 0,
    id: newId,
    local: !0,
    self: "out",
    t: parseInt(new Date().getTime() / 1000),
    to: id,
    isNewMsg: !0,
  }

  if (Array.isArray(vcard)) {
    Object.assign(extend, {
      type: "multi_vcard",
      vcardList: vcard
    });

    delete extend.body;
  } else {
    Object.assign(extend, {
      type: "vcard",
      subtype: vcard.displayName,
      body: vcard.vcard
    });

    delete extend.vcardList;
  }

  Object.assign(tempMsg, extend);

  const Sender = getStore().MessageSender2

  const chat = getWAChatById(id)

  // WARNING: do not put anything async from here until addAndSendMsgToChat
  const messagePromise = new Promise((resolve) => {
    onMessageSent(chat, resolve)
  })
  Sender.addAndSendMsgToChat(chat, tempMsg)

  return messagePromise
}

/**
 * Send a VCard as message
 * @example
 * ```javascript
 * // single contact
 * WPP.chat.sendVCardContactMessage('[number]@c.us', {
 *   id: '<EMAIL>',
 *   name: 'The Contact Name'
 * });
 *
 * // multiple contacts
 * WPP.chat.sendVCardContactMessage('[number]@c.us', [
 *   {
 *     id: '<EMAIL>',
 *     name: 'The Contact Name'
 *   },
 *   {
 *     id: '<EMAIL>',
 *     name: 'Another Contact'
 *   },
 * ]);
 *
 * ```
 * @category Message
 */
export async function sendVCardContactMessage(chatId, contacts) {
  const userPrefs = getStore().UserPrefs
  const vCardUtils = getStore().VCardUtils
  const ephemeralUtils = getStore().GetEphemeralFieldsMsgActionsUtils
  const contactStore = getStore().Contact
  const sender = getStore().MessageSender2
  const seenSender = getStore().SeenSender
  const chatStore = getStore().Chat
  const vcards = [];

  if (!Array.isArray(contacts)) {
    contacts = [contacts];
  }

  for (const contact of contacts) {
    let id = '';
    let name = '';


    if (typeof contact === 'object') {
      id = contact.id.toString();
      if ('name' in contact) {
        name = contact.name;
      }
    } else {
      id = contact.toString();
    }


    let contactModel = contactStore.get(id)

    if (!contactModel) {
      contactModel = new contactStore.modelClass({
        id: createWid(id),
        name,
      });
    }

    if (!name) {
      const maybeMeUser = userPrefs.getMaybeMeUser()

      if (contactModel.id.equals(maybeMeUser)) {
        name = contactModel.displayName;
      }
    }

    if (name) {
      // Create a clone
      contactModel = new contactStore.modelClass(contactModel.attributes);
      contactModel.name = name;
      Object.defineProperty(contactModel, 'formattedName', { value: name });
      Object.defineProperty(contactModel, 'displayName', { value: name });
    }

    vcards.push(vCardUtils.vcardFromContactModel(contactModel));
  }

  let chat = chatStore.get(chatId);

  const ephemeral = ephemeralUtils.getEphemeralFields(chat);

  const message = {
    ...ephemeral,
  };

  if (vcards.length === 1) {
    message.type = 'vcard';
    message.body = vcards[0].vcard;
    message.vcardFormattedName = vcards[0].displayName;
  } else {
    message.type = 'multi_vcard';
    message.vcardList = vcards;
  }

  const preparedMessage = await prepareMessage(chat, message)

  // Help to prevent bans
  // Try to mark is read and ignore errors
  await seenSender.sendSeen(chat, false).catch(() => null)

  console.log(`marking chat is read before send message`);

  const result = await sender.addAndSendMsgToChat(chat, preparedMessage)

  const sentMessage = await result[0];

  onMessageSent(chat, console.log)

  console.log(`waiting ack for ${preparedMessage.id}`);

  const sendResult = await result[1];

  console.log(
    `ack received for ${preparedMessage.id}, ACK: ${sentMessage.ack}, SendResult:`, sendResult
  );

  return transformMessage(preparedMessage)
}

export function sendLocation(id, lat, lng, loc) {

  const chat = getWAChatById(id)

  var tempMsg = {
    ack: 0,
    id: getNewMessageId(id),
    local: true,
    self: "in",
    t: parseInt(new Date().getTime() / 1000),
    to: chat.id,
    isNewMsg: true,
    type: "location",
    lat,
    lng,
    loc,
    from: getStore().Conn.wid
  };

  const Sender = getStore().MessageSender2

  // WARNING: do not put anything async from here until addAndSendMsgToChat
  const messagePromise = new Promise((resolve) => {
    onMessageSent(chat, resolve)
  })

  Sender.addAndSendMsgToChat(chat, tempMsg)

  return messagePromise

}

export function sendButton(id, content, title, footer, buttons) {

  const chat = getWAChatById(id)

  //   var tempMsg = {
  //     id: getNewMessageId(id),
  //     body: "Confirma para mim o seu nome *completo*: menu principal, está correto(a)?",
  //     type: "buttons",
  //     subtype: "text",
  //     t: parseInt(new Date().getTime() / 1000),
  //     notifyName: "",
  //     to: chat.id,
  //     self: "in",
  //     ack: 0,
  //     from: getStore().Conn.wid,
  //     invis: true,
  //     star: false,
  //     caption: "Confirma para mim o seu nome *completo*: menu principal, está correto(a)?",
  //     broadcast: false,
  //     mentionedJidList: [],
  //     isFromTemplate: true,
  //     __x_hasTemplateButtons: true,
  //     __x_isQuickReply: false,
  //     __x_isUserCreatedType: true,
  //     __x_buttons: [
  //         {
  //             "id": "0",
  //             "displayText": "Sim, está correto(a)",
  //             "subtype": "quick_reply",
  //             "selectionId": "{\"eventName\":\"yes\"}"
  //         },
  //         {
  //             id: "1",
  //             displayText: "Não, quero corrigir",
  //             subtype: "quick_reply",
  //             selectionId: "{\"eventName\":\"no\"}"
  //         }
  //     ],
  //     isForwarded: false,
  //     labels: []
  // }

  var tempMsg = {
    t: parseInt(new Date().getTime() / 1000),
    from: getStore().Conn.wid,
    isNewMsg: !0,
    to: chat.id,
    self: "out",
    type: 'buttons',
    ack: 0,
    local: !0,
    id: getNewMessageId(id),
  }

  let buttonsArray = [];
  for (let button of buttons.slice(0, 3)) {
    buttonsArray.push({
      buttonId: getNewMessageId(id),
      buttonText: {
        'displayText': button,
      },
      type: 1
    });
  }

  Object.assign(tempMsg, {
    productHeaderImageRejected: false,
    isFromTemplate: false,
    isDynamicReplyButtonsMsg: true,
    title: title,
    footer: footer,
    dynamicReplyButtons: buttonsArray,
    replyButtons: buttonsArray
  });

  if (!content) {
    content = ' ';
  }

  tempMsg.body = content;

  const Sender = getStore().MessageSender2

  // WARNING: do not put anything async from here until addAndSendMsgToChat
  const messagePromise = new Promise((resolve) => {
    onMessageSent(chat, resolve)
  })

  Sender.addAndSendMsgToChat(chat, tempMsg)

  return messagePromise
}

export function sendList(id, listOption) {

  const chat = getWAChatById(id)

  var newId = getNewMessageId(id);

  var extend = {
    t: parseInt(new Date().getTime() / 1000),
    from: getStore().Conn.wid,
    isNewMsg: !0,
    to: chat.id,
    self: "out",
    type: 'chat',
    ack: 0,
    local: !0,
    id: newId,
  }

  let sections = listOption.list.sections.map(section => {
    return {
      title: section.title,
      rows: section.rows.map(value => {
        return {
          rowId: getNewId(),
          title: value.title,
          description: value.description
        }
      })
    }
  });

  Object.assign(extend, {
    type: 'list',
    body: listOption.list.description,
    footer: listOption.list.footer,
    list: {
      description: listOption.list.description,
      buttonText: listOption.list.buttonText,
      title: listOption.list.title,
      footer: listOption.list.footer,
      sections: sections,
      listType: 1
    }
  });

  const Sender = getStore().MessageSender2

  // WARNING: do not put anything async from here until addAndSendMsgToChat
  const messagePromise = new Promise((resolve) => {
    onMessageSent(chat, resolve)
  })

  Sender.addAndSendMsgToChat(chat, extend)

  return messagePromise

}

// WhatsApp Low Level API below

/**
 * @param contact {WAContact}
 * @return {Promise<string|null>}
 */
export const getAvatarUrl = async (contact) => {
  if (contact.getProfilePicThumb && contact.getProfilePicThumb() && contact.getProfilePicThumb().eurl) {
    return contact.getProfilePicThumb().eurl
  }

  return null
}
/**
 * @param chat {WAChat}
 * @return {Promise<{
 *   groupParticipants: Contact[]
 * }>}
 */
export async function getGroupMeta(chat) {
  if (!chat.isGroup) return null

  if (!chat.groupMetadata.participants._models.length) {
    await fetchGroupParticipants(chat.contact.id)
  }

  const participants = chat.groupMetadata.participants._models.map(p => ({
    ...transformContactOmitFalsy(p.contact),
    isAdmin: p.isAdmin
  }))

  return {
    iAmAdmin: chat.groupMetadata.participants.iAmAdmin(),
    participants,
  }
}

export async function getContactMessagesMeta(chat, messagesUntil) {
  const timestamp = new Date(messagesUntil).getTime() / 1000

  await loadEarlierMessagesTillDate(chat, timestamp)

  const formattedMessages = []

  await queuedAsyncMap(chat.msgs._models, async (message) => {
    if (message.t > timestamp) {
      const formattedMessage = await transformMessageWithContactOmitFalsy(message)
      formattedMessages.push(formattedMessage)
    }
  }, 1000)

  return {
    messages: formattedMessages,
  }
}

export async function getCompleteChat(chat, messagesUntil) {
  if (!chat) throw new Error('Chat is required')

  const contact = chat.contact

  // needs to come before to give time to group attributes to load right after its creation
  const avatarUrl = await getAvatarUrl(contact)

  const formattedContact = transformContactOmitFalsy(contact)

  return {
    ...formattedContact,
    avatarUrl,
    ...(chat.isGroup && await getGroupMeta(chat)),
    ...(messagesUntil && await getContactMessagesMeta(chat, messagesUntil))
  }
}

export async function getCompleteChatById(id, messagesUntil) {
  const chat = getWAChatById(id)

  if (!chat) throw new Error('Chat not found.')

  return getCompleteChat(chat, messagesUntil)
}

export async function getCompleteChats(messagesUntil) {
  const chats = getWAChats()

  return queuedAsyncMap(chats, async (chat) => {
    return getCompleteChat(chat, messagesUntil)
  }, 100)
}

async function getAndValidateGroupChat(groupId) {
  const chat = await getWAChatById(groupId)
  if (!chat || (!chat.isGroup && chat.id.server != 'g.us')) throw new Error(`No group was found with provided ID.`)
  if (!chat.groupMetadata.participants.iAmAdmin()) throw new Error(`Not a group admin.`)
  return chat
}

function findGroupParticipantById(chat, participantId) {
  return chat.groupMetadata.participants.get(stringIdToObjectId(participantId))
}

function findGroupParticipantsByIds(chat, participantIds) {
  return participantIds.filter(participantId => findGroupParticipantById(chat, participantId))
}

function getGroupParticipantsByIds(chat, participantIds) {
  return participantIds.map(participantId => findGroupParticipantById(chat, participantId))
}

function createParticipantModel(id) {
  const ParticipantModel = getStore().ParticipantModel

  return new ParticipantModel({
    id: stringIdToObjectId(id),
  })
}

function findAdminGroupParticipantsByIds(chat, participantIds) {
  return participantIds.filter(participantId => {
    const p = findGroupParticipantById(chat, participantId)
    return p.isAdmin
  })
}

function findNotAdminGroupParticipantsByIds(chat, participantIds) {
  return participantIds.filter(participantId => {
    const p = findGroupParticipantById(chat, participantId)
    return !p.isAdmin
  })
}

function formatGroupActionResLow(res) {
  if (res.status >= 300 || !Array.isArray(res.participants)) throw new Error('Failed to execute action.')

  return res.participants.reduce((aggr, p) => {
    aggr[objectIdToStringId(p.userWid)] = parseInt(p.code) < 300
    return aggr
  }, {})
}

async function blockedContact(participants) {
  return participants
    .filter((participant) => {
      const waContact = getContactById(participant.id);
      return waContact && waContact.isContactBlocked;
    })
    .map((blocked) => {
      return { contactId: blocked.id, success: false, error: 'BLOCKED_CONTACT' };
    });
}

function formatGroupActionRes(res) {
  if (!Array.isArray(res.participants)) throw new Error('Failed to execute action.')

  return res.participants.reduce((aggr, p) => {
    aggr[objectIdToStringId(p.wid)] = !p.error
    return aggr
  }, {})
}

function formatGroupActionResAllSuccess(participants) {
  return participants.reduce((aggr, id) => {
    aggr[id] = true
    return aggr
  }, {})
}

async function validateParticipantsAreInTheGroup(chat, participantIds) {
  const filteredParticipants = await findGroupParticipantsByIds(chat, participantIds)
  if (filteredParticipants.length !== participantIds.length) throw new Error(`Some of the contacts are not in the group.`)
}

async function validateParticipantsAreNotInTheGroup(chat, participantIds) {
  const filteredParticipants = await findGroupParticipantsByIds(chat, participantIds)
  if (filteredParticipants.length) throw new Error(`Some of the contacts are already in the group.`)
}

async function validateParticipantsAreNotAdmin(chat, participantIds) {
  const filteredParticipants = await findAdminGroupParticipantsByIds(chat, participantIds)
  if (filteredParticipants.length) throw new Error(`Some of the contacts are not an admin.`)
}

async function validateParticipantsAreAdmin(chat, participantIds) {
  const filteredParticipants = await findNotAdminGroupParticipantsByIds(chat, participantIds)
  if (filteredParticipants.length) throw new Error(`Some of the contacts are already an admin.`)
}

window.__pendingGroupCreationSet = new Set()

export async function createGroup2(name, participantIds) {
  if (name.length > 100) {
    throw new Error('Name too long. Max 100 characters.')
  }

  if (window.__pendingGroupCreationSet.has(name)) {
    throw Error('Already creating a group with that name.')
  }

  window.__pendingGroupCreationSet.add(name)

  try {
    const results = await queuedAsyncMap(
      participantIds,
      async (participantId) => {
        const validId = await getValidId(participantId)
        return { id: participantId, validId }
      },
    )

    const valids = results.filter((r) => r.validId)
    const validContactsIds = valids.map((r) => r.validId)

    const invalidContactsIds = results.filter((r) => !r.validId).map(r => r.id)

    if (!valids.length) {
      throw new Error('Cannot create the group, all the contacts are invalid.')
    }

    let id

    const creationDate = new Date()

    const creationData = compare(self.Debug.VERSION, '2.3000.1014489107', '>=') ? {
      title: name,
      ephemeralDuration: 0,
      restrict: true,
      announce: true,
      membershipApprovalMode: false,
      memberAddMode: true,
      addressingModeOverride: "lid",
    } : name

    const res = await Promise.race([
      getStore().sendCreateGroup(creationData, validContactsIds.map(id => {
        if(id?.endsWith("@lid")){
          return { lid: stringIdToObjectId(id) }
        }
        return { phoneNumber: stringIdToObjectId(id) }
      }), 2),
      wait(45 * 1000).then(() => 'TIMEOUT'),
    ])

    if (res === 'TIMEOUT') {
      console.warn('sendCreateGroup timeout, polling by name...', name, participantIds)
      const g = await polling(
        () => getWAChats().find((chat) => chat.groupMetadata.name === name && new Date(chat.groupMetadata.creation * 1000) >= creationDate),
        { timeout: 45 * 1000, interval: 100 },
      ).catch(() => {
        throw new Error('Timeout out waiting for group to be created.')
      })
      id = g.id
    } else {
      await polling(() => getWAChatById(res.wid._serialized), { timeout: 45 * 1000, interval: 100 })
        .catch(() => {
          throw new Error('Timeout out waiting for group to be created.')
        })

      id = res.wid._serialized
    }

    return {
      id,
      participants: {
        ...invalidContactsIds.reduce((aggr, id) => {
          aggr[id] = false
          return aggr
        }, {}),
        ...formatGroupActionRes(res),
      }
    }
  } finally {
    window.__pendingGroupCreationSet.delete(name)
  }
}

async function handleFallbackTimeoutResponse(res, name, validContactsIds, invalidContactsIds, creationDate) {
  console.warn('sendCreateGroup timeout, polling by name...', name, validContactsIds)
  const g = await polling(
    () => getWAChats().find((chat) => chat.groupMetadata.name === name
      && new Date(chat.groupMetadata.creation * 1000) >= creationDate
    ),
    { timeout: 45 * 1000, interval: 100 },
  ).catch(() => {
    throw new Error('Timeout out waiting for group to be created.')
  })

  const groupId = g.id

  const groupParticipants = await fetchGroupParticipants(groupId)

  const contactsSuccessAdded = groupParticipants.participants
    .filter(p => !p.isAdmin)
    .map(p => ({ contactId: objectIdToStringId(p.id), success: true }))

  const contactsInvalids = invalidContactsIds
    .map(p => ({ contactId: p, success: false, error: 'INVALID_CONTACT_ID' }))//1º cause

  const failedContactIds = validContactsIds.filter(contactId => {
    return !contactsSuccessAdded.some(obj => obj.contactId === contactId)
      && !contactsInvalids.some(obj => obj.contactId === contactId)
  })

  const contactErrors = failedContactIds.map(contactId => {
    return { contactId, success: false, error: 'BLOCKED_CONTACT' }
  })

  return {
    id: groupId,
    participants: [
      ...contactsSuccessAdded,
      ...contactsInvalids,
      ...contactErrors
    ]
  }
}

export async function createGroupV2(name, participantIds) {
  if (name.length > 100) {
    throw new Error('Name too long. Max 100 characters.')
  }

  if (window.__pendingGroupCreationSet.has(name)) {
    throw Error('Already creating a group with that name.')
  }

  window.__pendingGroupCreationSet.add(name)

  try {
    const results = await queuedAsyncMap(
      participantIds,
      async (participantId) => {
        const validId = await getValidId(participantId)
        return { id: participantId, validId }
      },
    )

    const valids = results.filter((r) => r.validId)
    const blockedContacts = await blockedContact(valids)
    const blockedContactsIds = blockedContacts.map(contact => contact.contactId);
    const filteredValids = valids.map((r) => r.validId)
    const validContactsIds = filteredValids.filter(contactId => !blockedContactsIds.includes(contactId));
    const invalidContactsIds = results.filter((r) => !r.validId).map(r => r.id)

    if (!valids.length) {
      throw new Error('Cannot create the group, all the contacts are invalid.')
    }

    const creationDate = new Date()

    // refatorar (não funciona assim)
    const creationData = compare(self.Debug.VERSION, '2.3000.1014489107', '>=') ? {
      title: name,
      ephemeralDuration: 0,
      restrict: true,
      announce: true,
      membershipApprovalMode: false,
      memberAddMode: true,
      addressingModeOverride: "lid",
    } : name

    const res = await Promise.race([
      // 0 = ephemeral messages disabled
      getStore().sendCreateGroup(creationData, validContactsIds.map(id => {
        if(id?.endsWith("@lid")){
          return { lid: stringIdToObjectId(id) }
        }
        return { phoneNumber: stringIdToObjectId(id) }
      }), 0),
      wait(45 * 1000).then(() => 'TIMEOUT'),
    ]).catch(e => {
      console.error(e)

      if (e.message === 'rate-overlimit') {
        throw new Error('Failed to create group, rate limited.')
      }

      if (e.name === 'ServerStatusCodeError') {
        throw new Error(`Failed to create group.`)
      }

      throw e
    })

    if (res === 'TIMEOUT') {
      console.error("Timeout error, response:", res, ", name:", name, ", invalidContactsIds", invalidContactsIds)//log control timeout
      return handleFallbackTimeoutResponse(res, name, validContactsIds, invalidContactsIds, creationDate)
    }

    const groupId = res.wid._serialized

    await polling(
      () => getWAChatById(groupId),
      { timeout: 45 * 1000, interval: 100 },
    ).catch(() => {
      throw new Error('Timeout out waiting for group to be created.')
    })

    const invalidContactsRes = invalidContactsIds
      .map(p => ({ contactId: p, success: false, error: 'INVALID_CONTACT_ID' }))//2º cause

    if(invalidContactsRes.length>0 || invalidContactsIds.length>0){
    console.error("Invalid error, response:", res, ", name:", name, ", invalidContactsIds:", invalidContactsIds, ", invalidContactsRes:", invalidContactsRes)//log control Invalid error
    }
    const errorMap = {
      401: 'CONTACT_BLOCKED_ME',
      403: 'FORBIDDEN'
    }

    const getErrorCode = (contactId, status) => {


      if (status === 403) {
        const waContact = getWAContactById(contactId)

        if (!waContact || !waContact.isAddressBookContact) {
          return 'CONTACT_NOT_IN_ADDRESS_BOOK'
        }
      }

      return errorMap[status] || 'ERROR'
    }

    const contactsRes = res.participants.map(p => {
      const contactId = objectIdToStringId(p.wid)
      return {
        contactId,
        success: !p.error,
        ...(p.error && {
          error: getErrorCode(contactId, p.error),
        }),
        ...(p.invite_code && { inviteCode: p.invite_code }),
        ...(p.invite_code_exp && { inviteCodeExpiration: p.invite_code_exp })
      }
    })

    return {
      id: groupId,
      participants: [
        ...invalidContactsRes,
        ...contactsRes,
        ...blockedContacts
      ]
    }
  } finally {
    window.__pendingGroupCreationSet.delete(name)
  }
}

export async function addGroupParticipants(groupId, participantIds) {
  const chat = await getAndValidateGroupChat(groupId)
  await fetchGroupParticipants(groupId)

  const results = await queuedAsyncMap(
    participantIds,
    async (participantId) => {
      const validId = await getValidId(participantId)
      return { id: participantId, validId }
    },
  )
  const valids = results.filter((r) => r.validId)
  const invalidContactsIds = results.filter((r) => !r.validId).map(r => r.id)
  const validContactsIds = valids.map((r) => r.validId)

  await validateParticipantsAreNotInTheGroup(chat, validContactsIds)
  const participants = validContactsIds.map(createParticipantModel)

  const res = !!participants.length
    && await getStore().GroupActions.addGroupParticipants(chat.id, participants.map(p => ({ phoneNumber: p.id })))

  if (res && Array.isArray(res.participants) && res.participants.length) {
    const successParticipantsIds = res.participants
      .filter((p) => parseInt(p.code) < 300)
      .map((p) => objectIdToStringId(p.userWid))

    await polling(
      async () => (await findGroupParticipantsByIds(chat, successParticipantsIds)).length === successParticipantsIds.length,
      { timeout: 45 * 1000, interval: 100 },
    ).catch(() => {
      console.error('Timeout out waiting for group participants to appear in the group.', groupId, participantIds)
    })
  }

  return {
    ...invalidContactsIds.reduce((aggr, id) => {
      aggr[id] = false
      return aggr
    }, {}),
    ...(res && formatGroupActionResLow(res)),
  }
}

export async function addGroupParticipantsV2(groupId, participantIds) {
  const chat = await getAndValidateGroupChat(groupId)

  await fetchGroupParticipants(groupId)

  const results = await queuedAsyncMap(
    participantIds,
    async (participantId) => {
      const validId = await getValidId(participantId)
      return { id: participantId, validId }
    },
  )

  const valids = results.filter((r) => r.validId)
  const blockedContacts = await blockedContact(valids)
  const blockedContactsIds = blockedContacts.map(contact => contact.contactId);
  const filteredValids = valids.map((r) => r.validId)
  const invalidContactsIds = results.filter((r) => !r.validId).map(r => r.id)
  const validContactsIds = filteredValids.filter(contactId => !blockedContactsIds.includes(contactId));
  const participantsAlreadyInGroup = await findGroupParticipantsByIds(chat, validContactsIds)
  const participantsNotInGroup = validContactsIds.filter(id => !participantsAlreadyInGroup.find(p => p === id))

  const participants = participantsNotInGroup
    .map(createParticipantModel)
    .map(p => ({ phoneNumber: p.id }))

  let res = { participants: [] }

  if (participants.length) {
    res = await getStore().GroupActions.addGroupParticipants(chat.id, participants)
  }

  if (res.participants.length) {
    const successParticipantsIds = res.participants
      .filter((p) => parseInt(p.code) < 300)
      .map((p) => objectIdToStringId(p.userWid))

    await polling(
      async () => (await findGroupParticipantsByIds(chat, successParticipantsIds)).length === successParticipantsIds.length,
      { timeout: 45 * 1000, interval: 100 },
    ).catch(() => {
      console.error('Timeout out waiting for group participants to appear in the group.', groupId, participantIds)
    })
  }

  const contactsAlreadyInGroupRes = participantsAlreadyInGroup
    .map(p => ({ contactId: p, success: false, error: 'CONTACT_ALREADY_IN_GROUP' }))

  const invalidContactsRes = invalidContactsIds
    .map(p => ({ contactId: p, success: false, error: 'INVALID_CONTACT_ID' }))//3° cause

  if(invalidContactsIds?.length >0 || contactsAlreadyInGroupRes?.length >0){
  console.error("When adding error, response:", res, ", participants:", participants, ", invalidContactsIds:", invalidContactsIds, " invalidContactsRes:", invalidContactsRes)//log control When adding
  }
  const getErrorCode = (contactId, status) => {
    if (status === '403') {
      const waContact = getWAContactById(contactId)

      if (!waContact || !waContact.isAddressBookContact) {
        return 'CONTACT_NOT_IN_ADDRESS_BOOK'
      }
    }

    const errorMap = {
      401: 'CONTACT_BLOCKED_ME',
      403: 'FORBIDDEN',
      406: 'BLOCKED_CONTACT',
    }
    return errorMap[status] || 'ERROR'
  }

  const contactsRes = res.participants.map(p => {
    const contactId = objectIdToStringId(p.userWid)
    return {
      contactId,
      success: p.code === '200',
      ...(p.code !== '200' && {
        error: getErrorCode(contactId, p.code),
      }),
      ...(p.invite_code && { inviteCode: p.invite_code }),
      ...(p.invite_code_exp && { inviteCodeExpiration: p.invite_code_exp })
    }
  })

  return {
    success: contactsRes.some(c => c.success),
    participants: [
      ...invalidContactsRes,
      ...contactsAlreadyInGroupRes,
      ...contactsRes,
      ...blockedContacts,
    ]
  }
}

/**
 * @param groupId {string}
 * @param options {Object}
 * @param options.inviteCode {string}
 * @param options.inviteCodeExpiration {number}
 * @param options.contactId {string}
 * @return {*}
 */
export async function sendGroupInviteMessage(groupId, options) {
  const group = getGroupById(groupId)

  const wid = createWid(options.contactId)

  const chat = await findOrCreateChat(wid)

  const newId = await getNewMessageId(wid)

  var payload = {
    t: parseInt(new Date().getTime() / 1000),
    from: getStore().UserPrefs.getMaybeMeUser(),
    to: wid,
    self: "out",
    isNewMsg: true,
    local: true,
    ack: 0,
    type: "groups_v4_invite",
    inviteCode: options.inviteCode,
    inviteCodeExp: parseInt(options.inviteCodeExpiration, 10),
    inviteGrp: groupId,
    inviteGrpName: group.name,
    id: newId
  }

  const Sender = getStore().MessageSender2

  // help to prevent bans
  const SeenSender = getStore().SeenSender
  await SeenSender.sendSeen(chat, false).catch(() => null)

  const response = await Sender.addAndSendMsgToChat(chat, payload)

  return response[0]
}

export async function removeGroupParticipants(groupId, participantIds) {
  const chat = await getAndValidateGroupChat(groupId)
  await fetchGroupParticipants(groupId)

  const results = await queuedAsyncMap(
    participantIds,
    async (participantId) => {
      const validId = await getValidId(participantId)
      return { id: participantId, validId }
    },
  )
  const valids = results.filter((r) => r.validId)
  const invalidContactsIds = results.filter((r) => !r.validId).map(r => r.id)
  const validContactsIds = valids.map((r) => r.validId)

  await validateParticipantsAreInTheGroup(chat, validContactsIds)

  const successIds = []

  const TIMEOUT = 100

  for (let participantId of validContactsIds) {
    const participant = findGroupParticipantById(chat, participantId)

    await Promise.race([
      getStore().GroupActions.removeGroupParticipants(chat.id, [participant.id])
        .then(() => successIds.push(participantId))
        .catch((err) => console.error('removeParticipants failed', groupId, participantId, err)),
      wait(15 * 1000)
    ])

    await new Promise((resolve => setTimeout(resolve, TIMEOUT)))
  }

  await polling(
    async () => !(await findGroupParticipantsByIds(chat, successIds)).length,
    { timeout: 60 * 1000, interval: 100 },
  ).catch(() => {
    throw new Error('Timeout out waiting for participants be removed from group.')
  })

  const failedIds = validContactsIds.filter(p => successIds.includes(p))

  return {
    ...invalidContactsIds.reduce((aggr, id) => {
      aggr[id] = false
      return aggr
    }, {}),
    ...failedIds.reduce((aggr, id) => {
      aggr[id] = false
      return aggr
    }, {}),
    ...formatGroupActionResAllSuccess(successIds)
  }
}

export async function promoteGroupParticipants(groupId, participantIds) {
  const chat = await getAndValidateGroupChat(groupId)
  await fetchGroupParticipants(groupId)
  await validateParticipantsAreInTheGroup(chat, participantIds)
  await validateParticipantsAreNotAdmin(chat, participantIds)
  const participants = getGroupParticipantsByIds(chat, participantIds)

  const res = await getStore().GroupActions.promoteGroupParticipants(chat.id, participants.map(p => p.id))

  await polling(
    async () => participantIds.map((p) => findGroupParticipantById(chat, p)).every(p => p.isAdmin),
    { timeout: 60 * 1000, interval: 100 }
  ).catch(() => {
    throw new Error('Timeout out waiting for participants to become admin.')
  })

  return formatGroupActionResAllSuccess(participantIds)
}

export async function demoteGroupParticipants(groupId, participantIds) {
  const chat = await getAndValidateGroupChat(groupId)
  await fetchGroupParticipants(groupId)
  await validateParticipantsAreInTheGroup(chat, participantIds)
  await validateParticipantsAreAdmin(chat, participantIds)
  const participants = getGroupParticipantsByIds(chat, participantIds)

  await getStore().GroupActions.demoteGroupParticipants(chat.id, participants.map(p => p.id))

  await polling(
    async () => participantIds.map((p) => findGroupParticipantById(chat, p)).every(p => !p.isAdmin),
    { timeout: 60 * 1000, interval: 100 }
  ).catch(() => {
    throw new Error('Timeout out waiting for participants to be demoted from admin.')
  })

  return formatGroupActionResAllSuccess(participantIds)
}

async function forwardMessageWithObj(chat, message, overrideMessageId) {
  // WARNING: do not put anything async from here until forwardMessagesToChats
  const messagePromise = new Promise((resolve) => {
    onMessageSent(chat, resolve, overrideMessageId)
  })

  await getStore().ChatStore.forwardMessagesToChats([message], [chat], true)

  return messagePromise
}

export async function forwardMessage(chatId, messageId, overrideMessageId) {
  const chat = await getWAChatByIdOrAdd(chatId)
  const message = await getWAMessageByIdAsync(messageId)

  if (!message) throw new Error(`No message found with messageId "${messageId}".`)

  return forwardMessageWithObj(chat, message, overrideMessageId)
}

export async function forwardMessages(chatId, messageIds, overrideMessageIds) {
  if (overrideMessageIds && Array.isArray(overrideMessageIds) && messageIds.length !== overrideMessageIds.length) {
    throw new Error('messageIds.length differs from overrideMessageIds.length')
  }

  const chat = await getWAChatByIdOrAdd(chatId)
  const messages = await queuedAsyncMap(
    messageIds,
    (messageId) => getWAMessageByIdAsync(messageId).catch(res => messageId).then(m => m || messageId),
  )

  const failedMessages = messages.filter(m => typeof m === 'string')

  if (failedMessages.length) {
    throw new Error(`No message found with messageId "${failedMessages[0]}".`)
  }

  let res = []

  for (let i = 0; i < messages.length; i++) {
    const message = messages[i]
    const overrideMessageId = Array.isArray(overrideMessageIds) ? overrideMessageIds[i] : null

    const forwardedMessage = await forwardMessageWithObj(chat, message, overrideMessageId).catch(e => e)

    res.push(forwardedMessage)
  }

  return res
}