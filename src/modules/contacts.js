import memoize from './utils/memoize'
import { urlToBase64 } from './helpers'
import { getStore } from './store'
import queuedAsyncMap from './utils/queuedAsyncMap'
import {getMyId, versionGte} from './status'

/**
 * @typedef {Object} WAContact
 */

/**
 * @typedef {{
   * id: string,
   * name: string,
   * profileName: string,
   * number: string,
   * avatarUrl: string|null,
   * avatarBase64Url?: string
   * isMyContact: boolean,
   * isMe: boolean,
   * isWAContact: boolean,
   * widId: string
   * lidId : string
   * }} Contact
 */

/**
 * @param contact {WAContact}
 * @return {boolean}
 */
export function isMyContact(contact) {
  return contact.isMyContact
}

/**
 * @param number {string}
 * @return {string}
 */
export function anyNumberToContactId(number) {
  return `${String(number)}@c.us`
}

/**
 * @param id {string}
 * @return {string|null}
 */
export function contactIdToNumber(id) {
  if (typeof id !== 'string') return null
  return id?.split("@")?.[0]
}

export function addOrRemove9ForBrazilianNumber(number) {
    if (number.length === 13 && number[4] === '9') {
      return `${number.substr(0, 4)}${number.substr(5)}`
    }

    if (number.length === 12) {
      return `${number.substr(0, 4)}9${number.substr(4)}`
    }
    return number
}

/**
 * @param number {string|number}
 * @return {string[]|null}
 */
export function parseBrazilianNumber(number) {
  let match

  // 55 + DDD + 9 + NUMERO
  // 55 XX 9 XXXXXXXX
  match = number.match(/^(55)([1-9]{2})9([0-9]{8})$/)
  if (match) return [match[1], match[2], match[3]]

  // 55 + DDD + NUMERO
  // 55 XX XXXXXXXX
  match = number.match(/^(55)([1-9]{2})([0-9]{8})$/)
  if (match) return [match[1], match[2], match[3]]

  return null
}

/**
 * @param number {string}
 * @return {string}
 */
export function brazilianNumberToId(number) {
  const parsed = parseBrazilianNumber(number)
  if (!parsed) throw new Error('Not a valid format.')
  const [countryCode, areaCode, phoneNumber] = parsed

  const areaCodeHas9 = x => parseInt(x, 10) <= 28

  const fullNumber = `${countryCode}${areaCode}${
    areaCodeHas9(areaCode) ? '9' : ''
    }${phoneNumber}`

  return anyNumberToContactId(fullNumber)
}

/**
 * @param rawNumber {string}
 * @param handleBr {boolean}
 * @return {string}
 */
export function numberToContactId(rawNumber, handleBr = true) {
  const number = String(rawNumber).replace(/\D/g, '')

  if (handleBr && number.startsWith('55')) {
    return brazilianNumberToId(number)
  }

  return anyNumberToContactId(number)
}

export function parseContactId(rawId) {
  if (!rawId.endsWith('@c.us')) return rawId

  return numberToContactId(rawId.replace('@c.us', ''))
}

export function parseChatId(rawId) {
  if (rawId.endsWith('@g.us')) return rawId

  return parseContactId(rawId)
}

export function objectIdToStringId(id) {
  return typeof id === 'string' || !id
    ? id
    : id._serialized
}

export function stringIdToObjectId(id) {
  if(!id) return null
  const Wid = getStore().Wid

  return new Wid(id, { intentionallyUsePrivateConstructor: true })
}

/**
 * @param wContact {WAContact}
 * @return Contact
 */
export function transformContact(wContact) {
  const id = objectIdToStringId(wContact.id)

  return {
    id,
    name: wContact.name,
    profileName: wContact.pushname,
    number: stringIdToObjectId(getPhoneNumberById(wContact.id))?.user || wContact.id.user,
    avatarUrl: wContact.getProfilePicThumb && wContact.getProfilePicThumb()
      ? wContact.getProfilePicThumb().eurl
      : null,
    isMyContact: !!wContact.isAddressBookContact,
    isGroup: wContact.id.isGroup(),
    isMe: getPhoneNumberById(id) === getMyId(),
    //isWAContact: wContact.isWAContact,
    isBroadcast: wContact.isBroadcast,
    isEnterprise: wContact.isEnterprise,
    isContactBlocked: wContact.isContactBlocked,
    isBusiness: wContact.isBusiness,
    isAdmin: wContact.isAdmin,
    isSuperAdmin: wContact.isSuperAdmin,
    canSend: wContact.canSend,
    jidId: getPhoneNumberById(wContact.id),
    lidId: getLidById(wContact.id),
  }
}

export function transformContactOmitFalsy(contact) {
  return _.pickBy(transformContact(contact), _.identity)
}

/**
 * @return {WAContact[]}
 */
export function getWAContacts() {
  return getStore().Contact._models
}

/**
 * @param contact {Contact}
 * @return {Promise<Contact>}
 */
export function fetchContactAvatar(contact) {
  const id = stringIdToObjectId(contact.id)

  return Promise.resolve(contact.avatarUrl)
    .then(url => urlToBase64(url))
    .then(avatarBase64Url => ({
      ...contact,
      avatarBase64Url,
    }))
    .catch(() => contact)
}

/**
 * @param contacts {Contact[]}
 * @return {Promise<Contact[]>}
 */
export function fetchContactsAvatars(contacts) {
  return queuedAsyncMap(contacts, fetchContactAvatar)
}

/**
 * @param rawId {string}
 * @return {WAContact}
 */
export function addContactById(rawId) {
  const id = stringIdToObjectId(rawId)
  return getStore().Contact.add({ id }, { merge: true, add: true })
}

/**
 * @param onlyMyContacts {boolean}
 * @param onlyWithChat {boolean}
 * @return {Contact[]}
 */
export function getAllContacts(onlyMyContacts = false, onlyWithChat = false) {
  let contacts = onlyWithChat
    ? getStore().Chat._models.map(c => c.contact)
    : getWAContacts()

  if (onlyMyContacts) {
    contacts = contacts.filter(isMyContact)
  }

  return contacts.map(transformContact)
}

/**
 * @param onlyMyContacts {boolean}
 * @param onlyWithChat {boolean}
 * @return {Promise<Contact[]>}
 */
export function getAllContactsWithAvatars(onlyMyContacts = false, onlyWithChat = false) {
  const contacts = getAllContacts(onlyMyContacts, onlyWithChat)
  return fetchContactsAvatars(contacts)
}

/**
 * @param id {string}
 * @return {WAContact}
 */
export function getWAContactById(id) {
  const contacts = getWAContacts()

  for (let i = 0; i <= contacts.length - 1; i += 1) {
    if (objectIdToStringId(contacts[i].id) === id) {
      return contacts[i]
    }
  }

  return null
}

/**
 * @param id {string}
 * @return {Contact}
 */
export function getContactById(id) {
  const waContact = getWAContactById(id)
  return waContact && transformContact(waContact)
}

/**
 * @param id {string}
 * @return {Promise<Contact>}
 */
export function getContactByIdWithAvatar(id) {
  const contact = getContactById(id)
  return contact && fetchContactAvatar(contact)
}

/**
 * @param number {string}
 * @return {WAContact}
 */
export function addContactByNumber(number) {
  const id = numberToContactId(number)
  return addContactById(id)
}

/**
 * @param id {string}
 * @return {Promise<null|string>}
 */
export async function contactExistsById(id) {
  id = id[0] === '+' ? id.replace('+', '') : id
  const idPlus = '+' + id

  const res = id.endsWith('@lid') ? 
    await getStore().multiDeviceQueryExist({ type: 'lid', wid: stringIdToObjectId(id) }) : 
    await getStore().multiDeviceQueryExist({ type: 'phone', phone: idPlus })

  if (!res) return null

  return objectIdToStringId(res.wid)
}

/**
 * @param number {string}
 * @return {Promise<boolean>}
 */
export function contactExistsByNumber(number) {
  return contactExistsById(anyNumberToContactId(number)).then((id) => !!id)
}

/**
 * @return {WAContact[]}
 */
export function getWAContactGroups() {
  return getWAContacts().filter(c => c.id.server === 'g.us')
}

/**
 * @param cb {function(WAContact): void}
 */
export function onContactChange(cb) {
  return getStore().Contact.on('change', cb)
}

export const getValidId = memoize({ maxAge: 24 * 60 * 60 * 1000, isPromise: true  })(async (contactId) => {
  if (contactId.endsWith('@g.us')) {
      return getContactById(contactId)
        ? contactId
        : null
  }


  const result = await contactExistsById(contactId)

  const number = contactIdToNumber(contactId)

  if (
    !result &&
    contactId.startsWith('55') &&
    [12, 13].includes(number.length)
  ) {
    const altContactId = anyNumberToContactId(addOrRemove9ForBrazilianNumber(number))
    return contactExistsById(altContactId)
  }

  return result
})

/**
 * Recupera o Jid a partir do Lid
 * @param contactId {string}
 * @return {string/null}
 */
export function getPhoneNumberById(contactId){
  if(!contactId) return null
  const wid = typeof contactId == 'string' ? stringIdToObjectId(contactId) : contactId
  try {

    const phoneNumber = getStore().LidMigrationUtils.toPn(wid)
    return objectIdToStringId(phoneNumber)

  }catch(error){
    if(wid.isRegularUser()){
      console.warn(`Failed to getting phone number for id: ${wid.toString()}`)
    }    
    return null
  }
}

/**
 * Busca o Lid de um contato 
 * @param contactId {string} contactId
 * @return {string|null} @lid 
 */
export function getLidById(contactId){
  if (!contactId) return null
  const wid = typeof contactId == 'string'? stringIdToObjectId(contactId) : contactId
  try {

    const lidId = getStore().LidMigrationUtils.toLid(wid)
    
    return objectIdToStringId(lidId)

  }catch(error){
    if(wid.isRegularUser()){
      console.warn(`Failed to getting lidId for id: ${wid.toString()}`)
    }
    return null
  }
}