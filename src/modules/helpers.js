import { getStore } from './store'

/**
 * @param items {*[]}
 * @param promise {Function}
 * @return {Promise<*[]>}
 */
export function asyncMap(items, promise) {
  const newItems = []
  let curr = 0

  return new Promise((resolve, reject) => {
    const process = (p, i) =>
      p(items[i])
        .then((res) => {
          newItems.push(res)

          curr = +1

          if (curr === items.length) {
            resolve(newItems)
            return
          }

          process(promise, curr)
        })
        .catch(reject)

    process(promise, curr)
  })
}

/**
 * @param items {*[]}
 * @param promise {Function}
 * @return {Promise<*[]>}
 */
export function asyncFilter(items, promise) {
  const newItems = []
  let curr = 0

  return new Promise((resolve, reject) => {
    const process = (p, i) => {
      if (curr === items.length) {
        return resolve(newItems)
      }

      return p(items[i])
        .then((res) => {
          if (res) newItems.push(items[i])
          curr += 1
          process(promise, curr)
        })
        .catch(reject)
    }

    process(promise, curr)
  })
}

/**
 * @param blob {Blob}
 * @return {Promise<string>}
 */
export function blobToBase64DataUrl(blob) {
  return new Promise((resolve) => {
    const reader = new FileReader()

    reader.onload = () => {
      resolve(reader.result)
    }

    reader.readAsDataURL(blob)
  })
}

/**
 * @param blobUrl {string}
 * @return {Promise<string>}
 */
export async function blobUrlToBase64DataUrl(blobUrl) {
  const blob = await fetch(blobUrl).then(r => r.blob())
  return blobToBase64DataUrl(blob)
}

/**
 * @param b64Data {string}
 * @param contentType {string}
 * @param sliceSize {number}
 * @return {Blob}
 */
export function base64toBlob(b64Data, contentType = '', sliceSize = 512) {
  const byteCharacters = atob(b64Data)
  const byteArrays = []

  for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
    const slice = byteCharacters.slice(offset, offset + sliceSize)

    const byteNumbers = new Array(slice.length)
    for (let i = 0; i < slice.length; i += 1) {
      byteNumbers[i] = slice.charCodeAt(i)
    }

    const byteArray = new Uint8Array(byteNumbers)

    byteArrays.push(byteArray)
  }

  return new Blob(byteArrays, { type: contentType })
}

/**
 * @param blobData {ArrayBuffer|Blob|string}
 * @param name {string}
 * @param mimetype {string}
 * @return {File}
 */
export function createFile(blobData, name, mimetype) {
  return new File([blobData], name, { type: mimetype })
}

/**
 * @param base64 {string}
 * @param name {string}
 * @param mimetype {string}
 * @return {File}
 */
export function createFileFromBase64(base64, name, mimetype) {
  return createFile(base64toBlob(base64, mimetype), name, mimetype)
}

/**
 * @param url {string}
 * @return {Promise<string|null>}
 */
export function urlToBase64(url) {
  if (!url) return Promise.resolve(null)

  return window
    .fetch(url, { credentials: 'same-origin' })
    .then(response => {
      if (response.status >= 400) {
        throw new Error("Bad response from server")
      }

      return response.blob()
    })
    .then(blobToBase64DataUrl)
}

export function createWid(id) {
  return getStore().WidFactory.createWid(id) ?? null
}

export async function generateMessageID(chat) {
  const from = getStore().UserPrefs.getMaybeMeUser();
  let to;

  if (chat) {
    to = chat;
  } else {
    to = createWid(chat);
  }

  let participant = undefined;

  if (to?.__x_isGroup) {
    participant = getStore().WidFactory.toUserWid(from)
  }

  const MsgKey = getStore().MsgKey
  
  const msgId = await Promise.resolve(getStore().MsgKey.newId())
  
  const WidStore = getStore().Wid

  const remote = new WidStore(chat.id.toString(),{intentionallyUsePrivateConstructor: true})

  return new MsgKey({
    fromMe: true,
    selfDir: 'out',
    id: msgId,
    participant,
    remote,
    })
}

export async function prepareMessage(chat, message, options) {
  const newMessage = {
    type: 'chat',
    t: parseInt(new Date().getTime() / 1000),
    from: getStore().UserPrefs.getMaybeMeUser(),
    to: chat.id,
    self: 'out',
    isNewMsg: true,
    local: true,
    ack: 0,
    ...message,
  };


  if (!newMessage.id) {
    newMessage.id = await generateMessageID(chat);
  }

  return newMessage
}

export function getNewId() {
  var text     = "";
  var possible = "ABCDEF0123456789";

  for (var i = 0; i < 20; i++)
      text += possible.charAt(Math.floor(Math.random() * possible.length));
  return text;
};

/**
 * Create an chat ID based in a cloned one
 *
 * @param {string} cid '<EMAIL>'
 */
export function getNewMessageId(id) {
  var newMsgId = getStore().Msg._models[0].__x_id.clone();

  newMsgId.fromMe      = true;
  newMsgId.id          = getNewId().toUpperCase();
  newMsgId.remote      = getStore().UserPrefs.getMaybeMeUser()
  newMsgId._serialized = `${newMsgId.fromMe}_${newMsgId.remote}_${newMsgId.id}`

  return newMsgId;
};

export function downloadImg(url, type = 'image/jpeg', quality = 0.85){
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.src = url;

    img.onerror = reject;
    img.onload = () => {

      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (ctx) {
        canvas.height = img.naturalHeight;
        canvas.width = img.naturalWidth;

        ctx.drawImage(img, 0, 0);

        const data = canvas.toDataURL(type, quality);
      } else {
        console.error('Não foi possível obter o contexto 2D do canvas.');
      }

      resolve({
        data,
        height: img.naturalHeight,
        width: img.naturalWidth,
      });
    };
  });
}

export function getGlobalSettings() {
  return window.__fidelizza_settings__ || {}
}

export function handleFetchErrors(response) {
  if (!response.ok) {
    throw Error(response.statusText)
  }
  return response
}

export function wait(time) {
  return new Promise(resolve => {
    setTimeout(resolve, time)
  })
}
