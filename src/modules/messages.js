import { blobToBase64DataUrl, blobUrlToBase64DataUrl, getGlobalSettings, handleFetchErrors, wait } from './helpers'
import { getStore } from './store'
import { getPhoneNumberById, getWAContactById, objectIdToStringId, transformContact } from './contacts'
import { getWAChatByIdOrAdd } from './chats'
import polling from './utils/polling'
import {getMyId} from "./status";
import stringIsJson from './utils/stringIsJson'

/**
 * @typedef {Object} WAMessage
 */

/**
 * @typedef {{
 * id: string,
 * fullId: string,
 * partialId: string,
 * message: string|null,
 * contactId: string,
 * fromId: string,
 * toId?: string,
 * isFromMe: boolean,
 * timestamp: number,
 * ack: number,
 * type: string,
 * mimetype?: string,
 * size?: number,
 * filename?: string,
 * isNewMsg?: boolean,
 * isSentByMeFromWeb?: boolean,
 * lat?: number,
 * lng?: number,
 * mapPreviewUrl?: string,
 * vCard?: Object,
 * vCardString?: string,
 * previewUrl?: string,
 * previewMimetype?: string,
 * fullUrl?: string,
 * quotedMessage?: Message
 * }} Message
 */

/**
 * @param message {WAMessage}
 * @return {boolean}
 */
export function isMessage(message) {
  return message?.from?.user !== 'status'
}

export function isFullId(id) {
  return id.includes('_')
}

export function getMessageId(waMessage) {
  return waMessage.id._serialized === 'product_inquiry' ? waMessage.productId : waMessage.id._serialized
}

export function getLegacyMessageId(waMessage) {
  return waMessage.id.id === 'product_inquiry' ? waMessage.productId : waMessage.id.id
}

export function compareMessageId(waMessage, id) {
  if (isFullId(id)) return getMessageId(waMessage) === id
  return waMessage.id.id === id
}

export function stringIdToObjectId(id) {
  return getStore().MsgKey.fromString(id)
}

export function isSentByMe(waMessage) {
  const from = objectIdToStringId(waMessage.from)
  return getPhoneNumberById(from) === getMyId() 
}

export function isSentByMeFromThisDevice(waMessage) {
  return waMessage.self === 'out'
}

export function clearUnknownFieldCount(payload) {
  if (typeof payload === 'object' && payload !== null) {
    if (Array.isArray(payload)) {
      for (let i = 0; i < payload.length; i++) {
        payload[i] = clearUnknownFieldCount(payload[i]);
      }
    } else {
      for (const key in payload) {
        if (key === '$$unknownFieldCount') {
          delete payload[key];
        } else {
          payload[key] = clearUnknownFieldCount(payload[key]);
        }
      }
    }
  }
  return payload;
}
export function fixJSONStrings(payload) {
  if (typeof payload === 'object' && payload !== null) {
    if (Array.isArray(payload)) {
      for (let i = 0; i < payload.length; i++) {
        payload[i] = fixJSONStrings(payload[i]);
      }
    } else {
      for (const key in payload) {
        if (typeof payload[key] === 'string') {
          payload[key] = stringIsJson(payload[key]) ? JSON.parse(payload[key]) : payload[key];
        } else {
          payload[key] = fixJSONStrings(payload[key]);
        }
      }
    }
  }
  return payload;
}


export async function getChatFromMessage(waMessage) {
  const chatId = isSentByMe(waMessage) ? waMessage.to : waMessage.from

  return getWAChatByIdOrAdd(objectIdToStringId(chatId))
}

/**
 * @param waMessage {WAMessage}
 * @param isQuoted {boolean}
 * @return Promise<Message>
 */
export async function transformMessage(waMessage, isQuoted = false) {
  const chat = await getChatFromMessage(waMessage)

  const caption =
    waMessage.caption !== waMessage.filename ? waMessage.caption : null

  const usesFullMessageIds = getGlobalSettings().usesFullMessageIds

  const hasQuotedMessage = !!waMessage.quotedMsgKey || !!waMessage.quotedStanzaID
  const quotedMessageId = hasQuotedMessage && objectIdToStringId(waMessage.quotedMsgKey || waMessage.quotedStanzaID)
  const localQuotedMessage = hasQuotedMessage && getWAMessageById(quotedMessageId)
  const transformedQuotedMessage = localQuotedMessage && !isQuoted
    ? await transformMessage(localQuotedMessage, true)
    : null

  return {
    id: usesFullMessageIds ? getMessageId(waMessage) : getLegacyMessageId(waMessage),
    fullId: getMessageId(waMessage),
    partialId: getLegacyMessageId(waMessage),
    message: waMessage.type === 'chat' || waMessage.type === 'hsm' ? waMessage.body : caption,
    contactId: chat && (chat.contact.id.user === 'status' && waMessage.id.participant ? objectIdToStringId(waMessage.id.participant) : objectIdToStringId(chat.contact.id)),
    fromId: objectIdToStringId(waMessage.senderObj?.id || chat?.contact.id),
    isFromMe: isSentByMe(waMessage),
    timestamp: waMessage.t,
    ack: waMessage.ack,
    type: waMessage.type,
    mimetype: waMessage.mimetype,
    size: waMessage.size,
    filename: waMessage.filename,
    isNewMsg: waMessage.isNewMsg,
    // @legacy
    isSentByMeFromWeb: isSentByMeFromThisDevice(waMessage),
    isSentByMeFromThisDevice: isSentByMeFromThisDevice(waMessage),
    lat: waMessage.lat,
    lng: waMessage.lng,
    mapPreviewUrl:
      waMessage.type === 'location'
        ? `data:image/jpeg;base64,${waMessage.body}`
        : undefined,
    vCard: waMessage.type === 'vcard' ? waMessage.vcard : undefined,
    vcardList: waMessage.vcardList,
    vCardString: waMessage.type === 'vcard' ? waMessage.body : undefined,
    previewUrl: waMessage?.mediaData?.preview?.url?.() || (waMessage.mimetype && waMessage.body && `data:image/jpeg;base64,${waMessage.body}`),
    previewMimetype: waMessage?.mediaData?.preview?.type?.(),
    fullUrl: waMessage.__fullUrl,
    quotedMessage: transformedQuotedMessage,
    quotedMessageId,
    list: waMessage.type === 'list' ? clearUnknownFieldCount(fixJSONStrings(waMessage.list)) : undefined,
    dynamicReplyButtons: waMessage.isDynamicReplyButtonsMsg === true ? clearUnknownFieldCount(fixJSONStrings(waMessage.dynamicReplyButtons)) : undefined,
    ...(waMessage.title && ({
      product: {
        url: waMessage.url,
        title: waMessage.title,
        description: waMessage.description,
        retailerId: waMessage.retailerId,
        priceAmount1000: waMessage.priceAmount1000,
        currencyCode: waMessage.currencyCode,
      },
    })),
    // CTWA = Click to Whatsapp Ads
    ...(waMessage.ctwaContext && ({
      ctwaContext: {
        conversionSource: waMessage.ctwaContext.conversionSource,
        description: waMessage.ctwaContext.description,
        isSuspiciousLink: waMessage.ctwaContext.isSuspiciousLink,
        mediaType: waMessage.ctwaContext.mediaType,
        mediaUrl: waMessage.ctwaContext.mediaUrl,
        sourceUrl: waMessage.ctwaContext.sourceUrl,
        thumbnailUrl: waMessage.ctwaContext.thumbnailUrl,
        title: waMessage.ctwaContext.title,
      },
    })),
    hasFile: !!waMessage.mediaData,
    hasReaction: waMessage.hasReaction,
    mentionedJidList: waMessage.mentionedJidList,
    ...(waMessage.orderId && ({
      order: {
        totalAmount1000 : waMessage.totalAmount1000,
        totalCurrencyCode : waMessage.totalCurrencyCode,
        thumbnail : waMessage.thumbnail,
        itemCount : waMessage.itemCount,
        orderTitle : waMessage.orderTitle,
        orderId : waMessage.orderId,
      },
    })),
  }
}

/**
 * @param waMessage {WAMessage}
 * @return Promise<Message>
 */
export async function transformMessageAsync(waMessage) {
  const m = await transformMessage(waMessage)

  // get remote quoted message
  const quotedMessageNotLoaded = m.quotedMessageId && !m.quotedMessage
  const quotedMessageTimestampNotLoaded = m.quotedMessage && !m.quotedMessage?.timestamp

  if (quotedMessageNotLoaded || quotedMessageTimestampNotLoaded) {
    try {
      console.warn(
          `[${m.fullId}] [transformMessageAsync] Quoted message not found locally, falling back to remote fetch...`,
          { quotedMessageNotLoaded, quotedMessageTimestampNotLoaded }
      )

      await wait(1000)

      const remoteQuotedMessage = await getWAMessageByIdAsync(m.quotedMessageId)

      if (remoteQuotedMessage) {
        m.quotedMessage = await transformMessage(remoteQuotedMessage)
      }
    } catch (e) {
      console.error(`[${m.fullId}] [transformMessageAsync] Quoted message fetch failed.`, e)
    }
  }

  // get previewUrl async
  const getPreviewUrl = async (m) => {
    try {
      if (m?.previewUrl?.startsWith?.('blob:')) {
        console.log(`[${m.fullId}] [transformMessageAsync] Preview URL was a blob, converting to base64...`)
        return await blobUrlToBase64DataUrl(m.previewUrl)
      }

      return m?.previewUrl
    } catch (e) {
      console.error(`[${m.fullId}] [transformMessageAsync] getPreviewUrl failed.`, e)
      return null
    }
  }

  const previewUrl = await getPreviewUrl(m)
  const quotedPreviewUrl = m.quotedMessage && await getPreviewUrl(m?.quotedMessage?.previewUrl)
  
  return {
    ...m,
    ...(previewUrl && {
      previewUrl,
    }),
    timestamp: m.timestamp || (await getWAMessageFromSchemaById(m.id))?.t,
    quotedMessage: m.quotedMessage && {
      ...m.quotedMessage,
      ...(quotedPreviewUrl && {
        previewUrl: quotedPreviewUrl,
      }),
      timestamp: m.quotedMessage.timestamp || (await getWAMessageFromSchemaById(m.quotedMessageId))?.t,
    },
    transformer: 2,
  }
}

export async function transformMessageWithContact(wMessage, includeContact = true) {
  const chat = await getChatFromMessage(wMessage)

  const author =
    chat?.isGroup && wMessage.senderObj
      ? await getWAContactById(
        objectIdToStringId(wMessage.senderObj.id),
      )
      : null

  const message = {
    ...(await transformMessageAsync(
      wMessage,
    )),
    ...(includeContact && {
      contact: await transformContact(chat?.contact),
      ...(chat?.isGroup &&
        author && {
          from: await transformContact(author),
        }),
    }),
    ...(wMessage.vcardList && {
      vcardList: wMessage.vcardList,
    }),
  }

  return message
}

export async function transformMessageOmitFalsy(message) {
  return _.pickBy(await transformMessage(message), _.identity)
}

export async function transformMessageWithContactOmitFalsy(wMessage) {
  const message = await transformMessageWithContact(wMessage)

  const m = _.pickBy(message, _.identity)

  if (m.contact) {
    m.contact = _.pickBy(m.contact , _.identity)
  }

  if (m.from) {
    m.from = _.pickBy(m.from , _.identity)
  }

  return m
}

/**
 * @return {WAMessage[]}
 */
export function getWAMessages() {
  return getStore().Msg._models.filter(isMessage)
}

/**
 * @param id {string}
 * @return WAMessage
 */
export function getMessageById(id) {
  const messages = getWAMessages()

  return messages.filter(m => compareMessageId(m, id))[0]
}

export function downloadMessageMediaOld(waMessage) {
  const mediaData = waMessage.mediaData

  const download = () => waMessage.downloadMedia({
    downloadEvenIfExpensive: true,
    rmrReason: 1,
    isUserInitiated: true,
  })

  return download()
    .then(() => {
      if (mediaData.mediaBlob && mediaData.mediaBlob._blob) {
        return mediaData.mediaBlob._blob
      }

      const MediaBlobCache = getStore().MediaBlobCache

      const blobUrl = MediaBlobCache.getOrCreateURL(mediaData.filehash)
      return fetch(blobUrl).then(handleFetchErrors).then(r => r.blob())
    })
}

export async function downloadMessageMedia(msg) {
  if (!msg.mediaData) {
    throw new Error(`Message does not contain media. (${getMessageId(msg)})`);
  }

  await msg.downloadMedia({
    downloadEvenIfExpensive: true,
    rmrReason: 1,
    isUserInitiated: true,
  })

  if (['PREPARING', 'UPLOADING', 'SENDING'].includes(msg?.mediaData?.mediaStage)) {
    await polling(
        async () => !['PREPARING', 'UPLOADING', 'SENDING'].includes(msg.mediaData.mediaStage),
        { timeout: 25 * 1000, interval: 100 },
    ).catch(() => {
      console.log(`Timed out waiting for mediaStage. (Id: ${getMessageId(msg)}}, Stage: ${msg?.mediaData?.mediaStage})`, msg)
    })
  }

  let blob  = null

  if (msg.mediaData.mediaBlob && msg.mediaData.mediaBlob._blob) {
    blob = msg.mediaData.mediaBlob._blob
  } else if (msg.mediaData.mediaBlob) {
    blob = msg.mediaData.mediaBlob.forceToBlob()
  } else if (msg.mediaData.filehash) {
    const MediaBlobCache = getStore().MediaBlobCache
    blob = MediaBlobCache.get(msg.mediaData.filehash)
  }

  if (!blob && msg.mediaData.renderableUrl) {
    blob = await fetch(msg.mediaData.renderableUrl).then(r => r.blob())
  }

  if (!blob && msg.mediaObject?.type === 'VIDEO') {
    try {
      msg.type = 'document'
      msg.mediaObject.type = 'DOCUMENT'
      return await downloadMessageMedia(msg)
    } finally {
      msg.type = 'video'
      msg.mediaObject.type = 'VIDEO'
    }
  }

  return blob
}

/**
 * @param waMessage WAMessage
 * @return Promise<string|null>
 */
export function downloadMessageMediaByAsBase64(waMessage) {
  if (!waMessage.mediaData) return Promise.resolve(null)
  if (waMessage.__fullUrl) return Promise.resolve(waMessage.__fullUrl)

  return downloadMessageMedia(waMessage)
    .then(blobToBase64DataUrl)
    .then((dataUrl) => {
      waMessage.__fullUrl = dataUrl
      return waMessage.__fullUrl
    })
}

/**
 * @param waMessage WAMessage
 * @param uploadUrl string
 * @return Promise<string>
 */
export function downloadMessageMediaThenUpload(waMessage, uploadUrl) {
  if (!waMessage.mediaData) {
    console.log(`No mediaData`, waMessage)
    return Promise.resolve(null)
  }

  return downloadMessageMedia(waMessage)
    .then((blob) => {
      if (!blob) throw new Error('Blob is null')

      const file = new File(
        [blob],
        waMessage.filename,
        {
          lastModified: new Date().getTime(),
          type: waMessage.mimetype,
        },
      )

      return fetch(uploadUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'content-type': waMessage.mimetype,
        },
      })
    })
    .then(handleFetchErrors)
    .then(response => response.status)
}

/**
 * @param waMessage WAMessage
 * @param uploadUrl string
 * @param useProxy boolean
 * @return Promise<string>
 */
export async function downloadMessageMediaPreviewThenUpload(waMessage, uploadUrl, useProxy = true) {
  const previewObject = ((waMessage.mediaObject || {}).contentInfo || {}).preview

  if (!previewObject) return null

  const b64Url = previewObject.url()

  if (!b64Url) return null

  const blob = await fetch(b64Url).then((res) => res.arrayBuffer())

  const file = new File(
    [blob],
    waMessage.filename,
    {
      lastModified: new Date().getTime(),
      type: 'image/jpeg',
    },
  )

  return fetch(useProxy ? REQUEST_PROXY_URL : uploadUrl, {
      method: 'PUT',
      body: file,
      headers: {
        'content-type': 'image/jpeg',
        ...(useProxy && {
          [REQUEST_PROXY_HEADER]: uploadUrl,
        }),
      },
    })
  .then(handleFetchErrors)
  .then(response => response.status)
}

/**
 * @param id {string}
 * @return {Promise<string>}
 */
export function downloadMediaByMessageIdAsBase64(id) {
  const message = getMessageById(id)

  return downloadMessageMediaByAsBase64(message)
}

/**
 * @param id {string}
 * @param uploadUrl string
 * @return {Promise<string>}
 */
export function downloadMediaByMessageIdThenUpload(id, uploadUrl) {
  const message = getMessageById(id)

  return downloadMessageMediaByAsBase64(message, uploadUrl)
}

/**
 * @param waMessages {WAMessage[]}
 * @return {WAMessage[]}
 */
export function onlyUnreadLocal(waMessages) {
  const newMessages = []

  for (let i = waMessages.length - 1; i >= 0; i -= 1) {
    if (!waMessages[i] || waMessages[i]._fidelliza_read) continue
    newMessages.push(waMessages[i])
    // eslint-disable-next-line no-param-reassign
    waMessages[i]._fidelliza_read = true
  }

  return newMessages
}

/**
 * @param cb {function(WAMessage): void}
 */
export function onMessage(cb) {
  const store = getStore()
  if (!store) {
    console.error('[onMessage] Store not available!')
    return
  }

  if (!store.Msg) {
    console.error('[onMessage] Store.Msg not available!')
    return
  }

  if (typeof store.Msg.on !== 'function') {
    console.error('[onMessage] Store.Msg.on is not a function!')
    return
  }

  store.Msg.on('add', async (message) => {
    try {
      if (!isMessage(message)) return
      if (!message.isNewMsg) return

      if (message.type === 'ciphertext') {
        const id = getMessageId(message)

        const listener = () => {
          message.off('change:type', listener)
          cb(message)
        }
        message.on('change:type', listener)

        setTimeout(async ()=>{
          try {
            const chat = await getChatFromMessage(message)
            await getStore().Cmd.openChatBottom(chat)
              .then(()=>{
                getStore().Cmd.closeChat(chat)
              })
              .catch(console.error)
          } catch (error) {
            console.error(`[${id}] Error in ciphertext timeout handler:`, error)
          }
        }, 100)

        return
      }

      if (message?.mediaData && ['image', 'video'].includes(message.type)) {
        await polling(
          () => !!message?.mediaData?.preview?.type?.(),
          { interval: 50 },
        ).catch((e) => {
          console.error(e)
          // shh
        })
      }

      setTimeout(() => {
        try {
          cb(message)
        } catch (error) {
          console.error('[onMessage] Error in callback execution:', error)
        }
      }, 1)

    } catch (error) {
      console.error('[onMessage] Error in message handler:', error)
    }
  })
}

/**
 * @param cb {function(WAMessage): void}
 */
export function onMessageAckChange(cb) {
  getStore().Msg.on('change:ack', (message) => {
    if (!isMessage(message)) return
    cb(message)
  })
}

/**
 * @param waMessage
 * @param cb {function(Error|null, {message: WAMessage, fullUrl: string}): void}
 */
export function onWAMessageMediaLoad(waMessage, cb) {
  return downloadMessageMediaByAsBase64(waMessage).then((fullUrl) => {
    cb(null, {
      message: waMessage,
      fullUrl,
    })
  })
}

/**
 * @return {WAMessage[]}
 */
export function getLocalUnreadWAMessages() {
  return onlyUnreadLocal(getWAMessages())
}

/**
 * @return {WAMessage[]}
 */
export function getWAMessagesFromContactId(contactId) {
  // TODO optimize
  return getWAMessages().filter(m => objectIdToStringId(m.id.remote) === contactId)
}

/**
 * @return {WAMessage[]}
 */
export function getLocalUnreadWAMessagesFromContactId(contactId) {
  return onlyUnreadLocal(getWAMessagesFromContactId(contactId))
}

/**
 * @param waMessages {WAMessage[]}
 * @return {WAMessage[]}
 */
export async function transformMessages(waMessages) {
  return Promise.all(waMessages.map(transformMessage))
}

/**
 * @param waMessages {WAMessage[]}
 * @return {Promise<WAMessage[]>}
 */
export function transformMessagesAsync(waMessages) {
  return Promise.all(waMessages.map(transformMessageAsync))
}

/**
 * Retrieve message(s) from SchemaMessage
 * @param messageId {string | string[] | WAMessage | WAMessage[] | WAId | WAId[]}
 * @return Promise<WAMessage[]>
 */
export async function getWAMessagesFromSchemaByIds(messageId) {

  const messageIds = Array.isArray(messageId) ? messageId : [messageId]
  const filteredIds = (messageIds || []).map(m=> objectIdToStringId(m?.id) || objectIdToStringId(m)).filter(Boolean)
  
  if (filteredIds.length === 0) return []

  try {
    
    const messages = await getStore()
      .SchemaMessage
      .getMessageTable().$1()
      .table.filter(m => filteredIds.some(f=>m.id.includes(f)) && !!m.t)
      .toArray()

    return messages || []
  } catch (e) {
    console.error(`[getWAMessagesFromSchemaByIds] Error fetching messages from schema: ${e.message}`, e)
  }
  return []
}

/**
 * @param id {string | WAMessage | WAId}
 * @return Promise<WAMessage>
 */
export async function getWAMessageFromSchemaById(id){
    if(!id) return
    return (await getWAMessagesFromSchemaByIds(id))?.[0]
}

/**
 * @param id {string}
 * @return Promise<WAMessage>
 */
export async function getWAMessageByFullId(id) {
  if (!id) return null
  if (!isFullId(id)) throw new Error(`Not an full message id: "${id}".`)

  const idObj = stringIdToObjectId(id)

  // try local
  const localMsg = getStore().Msg.get(idObj)
  if (localMsg) return localMsg

  // fetch remote
  const chat = getStore().Chat.get(idObj.remote)
  if (!chat) return null

  const chatContext = getStore().SearchContext.getSearchContext(chat, idObj)
  if (!chatContext) return null

  await chatContext.collection.loadAroundPromise
  const msg = getStore().Msg.get(idObj)
  if (msg) return msg

  return  msg || null
}

/**
 * @param id {string}
 * @return WAMessage
 */
export function getWAMessageByPartialId(id) {
  if (!id) return null

  const messages = getStore().Msg._models

  for (let i = 0; i <= messages.length - 1; i += 1) {
    if (compareMessageId(messages[i], id)) {
      return messages[i]
    }
  }

  return null
}

/**
 * @param id {string}
 * @return WAMessage
 */
export function getWAMessageById(id) {
  if (!id) return null
  return getWAMessageByPartialId(id)
}

/**
 * @param id {string}
 * @return Promise<WAMessage>
 */
export async function getWAMessageByIdAsync(id) {
  if (!id) return null

  const message = isFullId(id)
    ? (await getWAMessageByFullId(id))
    : getWAMessageByPartialId(id)

  if(!message){
    console.warn(`[getWAMessageByIdAsync]: Message not found with id: ${id}`)
    return null
  }

  if(!message.t){
    const schemaMessage = await getWAMessageFromSchemaById(message.id)
    message.t = schemaMessage?.t
  }

  return message
}


export async function revokeMessageById(messageId) {
  const wMessage =
    await window.__fidelizza__.messages.getWAMessageByIdAsync(messageId)

  if (!wMessage) {
    throw new Error(`No message found with id ${messageId}`)
  }

  const MessageActions = getStore().MessageActions

  if (!MessageActions.canSenderRevokeMsg(wMessage) && !MessageActions.canAdminRevokeMsg(wMessage)) {
    throw new Error(`Cannot revoke message ${messageId}.`)
  }

  const chat = await getChatFromMessage(wMessage)

  const res = await getStore().RevokeSender.sendRevokeMsgs(chat, {list: [wMessage], type: 'message' }, {clearMedia: true})

  return res?.[0] === 'OK' || res?.[0]?.messageSendResult === 'OK'
}


