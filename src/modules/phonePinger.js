import { getStore } from './store'
import { getGlobalSettings, wait } from './helpers'

let interval = null
let pong = true

const PING_INTERVAL = getGlobalSettings().phonePingInterval || 1000 * 15
const PING_TIMEOUT = getGlobalSettings().phonePingTimeout || 1000 * 5

export function getPingerStatus() {
  return pong
}

export async function sendPing() {
  const State = getStore().State

  pong = await Promise.race([
    State.default
      .sendBasic({
        tag: State.default.tag('ping'),
        data: ['admin', 'test'],
      }).then((res) => Array.isArray(res?.data) && res?.data?.[1] === true),
    wait(PING_TIMEOUT).then(() => false),
  ])

  return pong
}

export function startPinger() {
  if (interval) return
  sendPing()
  interval = setInterval(sendPing, PING_INTERVAL)
}

export function stopPinger() {
  if (!interval) return
  clearInterval(interval)
  interval = null
}
