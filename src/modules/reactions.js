import EventEmitter from 'eventemitter3'
import { getStore } from './store'
import { objectIdToStringId } from './contacts'
import { getWAMessageByIdAsync } from './messages'
import queuedAsyncMap from './utils/queuedAsyncMap'

const events = new EventEmitter()
const eventOnReaction = 'on-reaction'

/**
 * Definir o evento de on reaction
 */
export function setEventOnReaction() {
  // Salvar a função original de updateReactionCollection
  const originalUpdateReactionCollection =
    getStore().ReactionMessages.updateReactionCollection

  // Definir uma nova função para updateReactionCollection
  getStore().ReactionMessages.updateReactionCollection = function (...params) {
    // Realizar a emissão do evento de on reaction
    events.emit(eventOnReaction, params)

    // Executar a função original e retornar seu respectivo resultado para manter o comportamento esperado
    return originalUpdateReactionCollection(...params)
  }
}

/**
 * Recuperar todas as reações
 */
export async function getWAReactionsAll() {
  return getStore().SchemaReactions.getReactionsTable().all()
}

/**
 * Recuperar reações por um array de parentMessageId
 * @param parentMessageId {string | string[]}
 */
export async function getWAReactionsFromMessageId(parentMessageId) {
  if (!parentMessageId) return []

  const parentMessageIds = Array.isArray(parentMessageId) ? parentMessageId : [parentMessageId]

  const parentMsgKeys = parentMessageIds.filter(Boolean)

  const reactions = await getStore()
    .SchemaReactions
    .getReactionsTable()
    .anyOf(['parentMsgKey'], parentMsgKeys)

  return queuedAsyncMap(reactions || [], (reaction) =>
    getStore().ReactionDataUtils.reactionRowToReactionMsgData(reaction)
  )
}

/**
 * Enviar uma reação
 * @param parentMessageId {string}
 * @param reactionEmojiRendered {string}
 */
export async function sendReactionByMessage(parentMessageId, reactionEmojiRendered) {
  const wMessage = await getWAMessageByIdAsync(parentMessageId)

  if (!wMessage) {
    throw new Error(`No message found with id ${parentMessageId}.`)
  }

  if (!getStore().ReactionUtils.canHaveReactions(wMessage)) {
    throw new Error(`Cannot react message ${parentMessageId}.`)
  }

  // Geralmente o retorno de sendReactionToMsg é undefined quando há sucesso
  return await getStore().ReactionActions.sendReactionToMsg(wMessage, reactionEmojiRendered) || true
}

/**
 * Revogar uma reação
 * @param parentMessageId {string}
 */
export async function revokeReactionByMessage(parentMessageId) {
  // Obter o texto para revogar uma reação (geralmente é uma string vazia)
  const revokedReactionText = getStore().ReactionUtils.REVOKED_REACTION_TEXT

  return await sendReactionByMessage(parentMessageId, revokedReactionText)
}

/**
 * Adicionar listener para os eventos de reações
 * @param cb {function(array): void}
 */
export function onReaction(cb) {
  // Adicionar o callback no listener de eventos
  events.addListener(eventOnReaction, cb)

  console.log(`Listener added for event ${eventOnReaction}`)
}

/**
 * Transformar uma reação para um objeto simplificado
 * @param wReaction {object}
 */
export async function transformReaction(wReaction) {
  // Obter o texto de uma reação revogada (geralmente é uma string vazia)
  const revokedReactionText = getStore().ReactionUtils.REVOKED_REACTION_TEXT

  // Verificar se a reação está revogada
  const isRevoked = wReaction.reactionText === revokedReactionText

  // Se existir author, é uma conversa em grupo. Se não existir, é uma conversa particular
  const from = wReaction.author ? wReaction.author : wReaction.from

  return {
    id: objectIdToStringId(wReaction.id),
    fullId: objectIdToStringId(wReaction.id),
    message: isRevoked ? '' : wReaction.reactionText, // se a reação foi revogada: string vazia, se não: o texto com a reação
    contactId: objectIdToStringId(wReaction.id?.remote), // contato: pessoa ou grupo
    fromId: objectIdToStringId(from),
    isFromMe: wReaction.id.fromMe,
    timestamp: wReaction.t, // em segundos
    ack: wReaction.ack, // 0 ou 1
    type: wReaction.type, // type reaction
    fullParentMessageIdFromService: wReaction.reactionParentKey._serialized, // id completo da mensagem no chat no qual está recebendo a reação
    partialParentMessageIdFromService: wReaction.reactionParentKey.id, // id parcial da mensagem no chat no qual está recebendo a reação
  }
}

/**
 * Transformar reações em um array de objetos simplificados
 * @param wReaction {object | array}
 */
export async function transformReactions(wReaction) {
  if (!wReaction) return []

  const wReactions = Array.isArray(wReaction) ? wReaction : [wReaction]

  const reactions = wReactions.filter(Boolean)

  return queuedAsyncMap(reactions, (reaction) => transformReaction(reaction))
}
