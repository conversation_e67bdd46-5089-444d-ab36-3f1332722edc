import { gte } from 'semver'
import {contactIdToNumber, objectIdToStringId} from './contacts'
import { getStore } from './store'
import { getPingerStatus } from './phonePinger'

/**
 * @return {boolean}
 */
export function isWebConnected() {
  return getStore().Stream.info !== 'OFFLINE'
}

/**
 * @return {number}
 */
export function getBatteryLevel() {
  return getStore().Conn.battery
}

/**
 * @return {boolean}
 */
export function isCharging() {
  return getStore().Conn.plugged
}

/**
 * @return {string}
 */
export function getMode() {
  return getStore().Stream.mode
}

/**
 * @return {string}
 */
export function getState() {
  return getStore().Stream.info
}

/**
 * @return {boolean}
 */
export function isWaitingForPhoneInternet() {
  return ['PAIRING', 'TIMEOUT'].includes(getStore().Stream.info)
}

export function isPhoneConnectedWA() {
  return getStore().Stream.obscurity === 'HIDE'
}

export function isPhoneConnectedPinger() {
  return getPingerStatus()
}

/**
 * @return {boolean}
 */
export function isPhoneConnected() {
  return isPhoneConnectedWA() && isPhoneConnectedPinger()
}

/**
 * @return {boolean}
 */
export function isConflicted() {
  return getMode() === 'CONFLICT'
}

/**
 * @return {boolean}
 */
export function isWebSyncing() {
  return getMode() === 'SYNCING'
}

/**
 * @return {boolean}
 */
export function isOnChatPage() {
  return getMode() === 'MAIN'
}

/**
 * @return {boolean}
 */
export function isOnQrPage() {
  return getMode() === 'QR'
}

/**
 * @return {boolean}
 */
export function isLoading() {
  return !!document.querySelector('#startup')
}

/**
 * @return {boolean}
 */
export function isPhoneAuthed() {
  return getStore().Stream.phoneAuthed
}

/**
 * @return {boolean}
 */
export function isConnected() {
  return isWebConnected() && isOnChatPage()
}

/**
 * @return {string}
 */
export function getMyId() {
  return getMyIdMD()
}

export function getMyIdMD() {
  const UserPrefs = getStore()?.UserPrefs
  const me = UserPrefs?.getMe?.()

  if (!me) return null

  const number = me.user

  return `${number}@c.us`
}

/**
 * @return {string}
 */
export function getMyNumber() {
  const id = getMyId()
  return contactIdToNumber(id)
}

/**
 * @return {string}
 */
export function getMyName() {
  return getStore().Conn.pushname
}

/**
 * @return {boolean}
 */
export function needsCharging() {
  return getBatteryLevel() < 15 && !isCharging()
}

/**
 * @return {number}
 */
export function getQrCodeExpiry() {
  return getStore().Conn.refExpiry
}

/**
 * @return {boolean}
 */
export function isQrCodeExpired() {
  return !!document.querySelector('[data-testid="refresh-large"]')
}

/**
 * @return {string|null}
 */
export function getQrCode() {
  if (isQrCodeExpired()) return null

  if (document.querySelector('img[alt="Scan me!"]')) {
    const el = document.querySelector('img[alt="Scan me!"]')
    return el && el.src
  }

  const el = document.querySelector("div[data-ref] canvas")
  return el && el.toDataURL()
}

export function getVersion() {
  return (window.Debug || {}).VERSION
}

export function versionGte(version) {
  return gte(getVersion(), version)
}
/**
 * @returns {boolean}
 */
export function isLidMigrated(){
  return getStore().Lid1X1MigrationUtils.isLidMigrated()
}

/**
 * @return {Object}
 */
export function getStatus() {
  return getStore() && {
    myId: getMyId(),
    myNumber: getMyNumber(),
    myName: getMyName(),
    isWebConnected: isWebConnected(),
    isPhoneConnected: isPhoneConnected(),
    isPhoneAuthed: isPhoneAuthed(),
    isWaitingForPhoneInternet: isWaitingForPhoneInternet(),
    isWebSyncing: isWebSyncing(),
    isOnChatPage: isOnChatPage(),
    isConnected: isConnected(),
    isCharging: isCharging(),
    isLoading: isLoading(),
    isOnQrPage: isOnQrPage(),
    isLidMigrated: isLidMigrated(),
    needsCharging: needsCharging(),
    batteryLevel: getBatteryLevel(),
    qrCodeUrl: getQrCode(),
    qrCodeExpiresAt: getQrCodeExpiry(),
    isQrCodeExpired: isQrCodeExpired(),
    isConflicted: isConflicted(),
    mode: getMode(),
    state: getState(),
    isInMultiDeviceMode: true,
    fullStore: getStore().full,
    waVersion: getVersion(),
    scriptsSrcVersion: window.__fidelizza__.srcVersion,
  }
}
