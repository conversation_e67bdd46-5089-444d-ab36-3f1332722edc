const promisify = (fn) => (...args) => new Promise((resolve, reject) => {
  try {
    resolve(fn(...args))
  } catch (e) {
    reject(e)
  }
})

/**
 * Continuously tests the function until it returns true or times out resulting in error
 * @param fn {function}
 * @param timeout {number}
 * @param interval {number}
 * @param logFail {boolean}
 * @returns {Promise}
 */
export default (fn, { timeout = 5000, interval = 200, logFail = false } = {}) => {
  if (!(fn instanceof Promise)) {
    fn = promisify(fn)
  }

  const endTime = Number(new Date()) + timeout

  const checkCondition = (resolve, reject) => {
    const retry = (err) => {
      if (logFail) console.error('polling error', err)
      if (Number(new Date()) < endTime) {
        setTimeout(checkCondition, interval, resolve, reject)
      } else reject(new Error(`Timed out for ${fn}`))
    }

    fn().then((result) => {
      if (!result) return retry()
      resolve(result)
    }).catch(retry)
  }

  return new Promise(checkCondition)
}
