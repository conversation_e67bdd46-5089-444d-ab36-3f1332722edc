import queue from 'async/queue'

// new array order may not be correct

export default(
  items,
  cb,
  parallel = 5,
)=>
new Promise((resolve, reject) => {
  const newItems = []

  const q = queue((item, callback) => {
    const index = items.indexOf(item)
    const maybePromise = cb(item, index)

    if (typeof maybePromise === 'object' && maybePromise.then) {
      maybePromise
        .then((newItem) => {
          newItems.splice(index, 0, newItem)
          callback()
        })
        .catch(reject)
    } else {
      newItems.splice(index, 0, maybePromise)
      callback()
    }
  }, parallel)

  q.drain(() => resolve(newItems))

  q.push(items)
})
