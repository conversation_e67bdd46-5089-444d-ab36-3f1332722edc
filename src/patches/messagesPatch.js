import { getStore } from '../modules/store'
import { findOrCreateChat } from '../modules/chats'

export default function applyMessagesPatch() {
  const originalCreateChatRecord = getStore().ApiChat.createChatRecord

  getStore().ApiChat.createChatRecord = async (...args) => {
    const maxAttempts = 5
    let delay = 1000

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await originalCreateChatRecord(...args)
      } catch (err) {
        console.warn(`[MessagePatch] createChatRecord attempt ${attempt} failed:`, err)

        if (attempt === maxAttempts) {
          console.error('[MessagePatch] createChatRecord failed after all attempts', err)
          throw err
        }

        console.log(`[MessagePatch] Retrying in ${delay}ms...`)

        await new Promise((resolve) => setTimeout(resolve, delay))
        delay *= 2
      }
    }
  }

  const originalFindExistingChat = getStore().FindChat.findExistingChat

  getStore().FindChat.findExistingChat = async (...args) => {
    const [chatId] = args
    
    const contact = getStore().Contact.get(chatId)
    let existingChat = await getStore().ChatGetExistingBridge.getExisting(chatId)

    console.log(`[MessagePatchChat] Chat id: ${chatId} - Same ids: ${contact?.id?._serialized === existingChat?.id?._serialized} -
      Contact initialized: ${contact?.__initialized} - Contact sync: ${contact?.__x_isContactSyncCompleted} -
      Chat can send: ${existingChat?.__x_canSend} - Chat not spam: ${existingChat?.__x_notSpam}`)
    
    if(existingChat?.id?.isLid() && !existingChat?.accountLid){
      console.warn(`[MessagePatch] Chat is lid without accountLid: ${chatId}`)
      await deleteChatById(chatId.toString())
      existingChat = null
    }

    if (!existingChat && contact) {
      try {
        console.warn(`[MessagePatch] Chat did not exists: ${chatId}`)
        await findOrCreateChat(chatId)
        console.log(`[MessagePatch] Chat created with success: ${chatId}`)
      } catch (error) {
        console.error(`[MessagePatch] Error creating chat: ${chatId}`, error)
      }
    }

    return await originalFindExistingChat(...args)
  }

  console.log('[MessagePatch] Messages patch applied.')
}
