# WhatsApp Scripts Tests

This folder contains tests for the WhatsApp Web injection library.

## Puppeteer Test

The `whatsapp-puppeteer-test.js` file contains a comprehensive test that:

1. **Opens WhatsApp Web** using Puppeteer in a visible browser
2. **Injects the WhatsApp scripts** library (`dist/index.js`)
3. **Waits for QR code** scanning and login
4. **Tests the API** functionality
5. **Sends a test message** (optional)

### Prerequisites

1. **Build the injection script first:**
   ```bash
   npm run build
   ```

2. **Install dependencies** (Puppeteer should already be installed):
   ```bash
   npm install
   ```

### Usage

#### Basic API Testing (No message sending)
```bash
node tests/whatsapp-puppeteer-test.js
```

#### Test with Message Sending
```bash
node tests/whatsapp-puppeteer-test.js "+1234567890" "Hello World!"
```

#### Session Persistence Options
```bash
# Use persistent session (default - only scan QR once)
node tests/whatsapp-puppeteer-test.js

# Start fresh (always scan QR)
node tests/whatsapp-puppeteer-test.js --fresh

# Use custom profile name
node tests/whatsapp-puppeteer-test.js --profile "my-profile"

# Clear saved profile and start fresh
node tests/whatsapp-puppeteer-test.js --clear-profile
```

Replace `+1234567890` with the target phone number (include country code).

### Test Flow

1. **Browser Launch**: Opens Chrome with persistent profile (if enabled)
2. **WhatsApp Web**: Navigates to https://web.whatsapp.com
3. **Script Injection**: Injects the built WhatsApp scripts
4. **Session Check**: Checks if already logged in from previous session
5. **QR Code**: Shows QR code only if not already logged in
6. **Manual Scan**: User scans QR code with their phone (first time only)
7. **Login Wait**: Waits for login completion and script initialization
8. **API Test**: Verifies all API modules are available
9. **Message Test**: Sends test message if phone number provided
10. **Manual Testing**: Keeps browser open for additional manual testing
11. **Session Save**: Saves login session for next time (if persistent mode)

### API Testing

The test verifies that the following API modules are available:
- `window.__fidelizza__.chats` - Chat operations
- `window.__fidelizza__.contacts` - Contact management  
- `window.__fidelizza__.messages` - Message handling
- `window.__fidelizza__.status` - Status information

### Manual Testing

After the automated test completes, you can manually test the API in the browser console:

```javascript
// Send a message
const contactId = window.__fidelizza__.contacts.numberToContactId("+1234567890");
await window.__fidelizza__.chats.sendMessageToId(contactId, "Hello from manual test!");

// Get contacts
const contacts = window.__fidelizza__.contacts.getContacts();

// Listen for new messages
window.__fidelizza__.messages.onMessage((message) => {
  console.log('New message:', message);
});
```

### Troubleshooting

**Script not found error:**
- Make sure to run `npm run build` first

**Login timeout:**
- Scan the QR code quickly when it appears
- Make sure your phone has internet connection

**API not available:**
- Wait for the script to fully initialize
- Check browser console for any errors

**Message sending fails:**
- Verify the phone number format (include country code)
- Make sure the contact exists or can be created
- Check if WhatsApp Web is fully loaded

### Session Persistence

**Default Behavior (Persistent):**
- Browser profile is saved in temp directory
- Login session persists between runs
- Only need to scan QR code once
- Profile location: `/tmp/whatsapp-puppeteer-profiles/whatsapp-test/`

**Fresh Mode:**
- No session persistence
- Always shows QR code
- Useful for testing or multiple accounts

**Profile Management:**
```bash
# Clear saved profile
node tests/whatsapp-puppeteer-test.js --clear-profile

# Use custom profile
node tests/whatsapp-puppeteer-test.js --profile "work-account"
```

### Customization

The test class can be extended or used programmatically:

```javascript
const WhatsAppPuppeteerTest = require('./tests/whatsapp-puppeteer-test');

// Basic usage with persistent session
const test = new WhatsAppPuppeteerTest();

// Custom options
const test = new WhatsAppPuppeteerTest({
  persistSession: false,    // Disable session persistence
  profileName: 'my-profile' // Custom profile name
});

await test.init();
await test.openWhatsAppWeb();
await test.injectScript();
// ... custom logic
await test.cleanup();

// Clear profile programmatically
test.clearProfile();
```

### Notes

- The browser runs in **non-headless mode** so you can see what's happening
- **Session persistence is enabled by default** - you only need to scan QR code once
- The test keeps the browser open after completion for manual testing
- Press Enter when prompted to close the browser
- The injection script is automatically rebuilt when you run `npm run build`
- Use `--fresh` flag to disable session persistence for testing
