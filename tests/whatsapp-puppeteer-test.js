const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const os = require('os');

/**
 * WhatsApp Web Puppeteer Test
 * 
 * This test:
 * 1. Opens WhatsApp Web using Puppeteer
 * 2. Injects the WhatsApp scripts library
 * 3. Waits for QR code scanning and login
 * 4. Tests sending a message
 */

class WhatsAppPuppeteerTest {
  constructor(options = {}) {
    this.browser = null;
    this.page = null;
    this.injectionScript = null;
    this.options = {
      persistSession: true,
      profileName: 'whatsapp-test',
      ...options
    };
    this.userDataDir = this.getUserDataDir();
  }

  getUserDataDir() {
    if (!this.options.persistSession) return null;

    const baseDir = path.join(os.tmpdir(), 'whatsapp-puppeteer-profiles');
    const profileDir = path.join(baseDir, this.options.profileName);

    // Create directory if it doesn't exist
    if (!fs.existsSync(baseDir)) {
      fs.mkdirSync(baseDir, { recursive: true });
    }
    if (!fs.existsSync(profileDir)) {
      fs.mkdirSync(profileDir, { recursive: true });
    }

    return profileDir;
  }

  async init() {
    console.log('🚀 Initializing WhatsApp Puppeteer Test...');

    if (this.options.persistSession) {
      console.log(`💾 Using persistent profile: ${this.userDataDir}`);
      await this.ensureProfileAvailable();
    }

    // Load the injection script
    const scriptPath = path.join(__dirname, '../dist/index.js');
    if (!fs.existsSync(scriptPath)) {
      throw new Error('Injection script not found. Please run "npm run build" first.');
    }
    this.injectionScript = fs.readFileSync(scriptPath, 'utf8');
    console.log('✅ Injection script loaded');

    // Launch browser with persistent profile
    const launchOptions = {
      headless: false, // Keep visible so user can scan QR code
      defaultViewport: null,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    };

    // Add user data directory for persistent sessions
    if (this.userDataDir) {
      launchOptions.userDataDir = this.userDataDir;
      // Add args to handle profile locks better
      launchOptions.args.push(`--user-data-dir=${this.userDataDir}`);
      launchOptions.args.push('--no-first-run');
      launchOptions.args.push('--disable-default-apps');
      console.log(`📁 Browser profile directory: ${this.userDataDir}`);
    }

    this.browser = await puppeteer.launch(launchOptions);
    this.page = await this.browser.newPage();

    // Set user agent to avoid detection
    await this.page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

    console.log('✅ Browser launched');
  }

  async openWhatsAppWeb() {
    console.log('🌐 Opening WhatsApp Web...');
    
    await this.page.goto('https://web.whatsapp.com', {
      waitUntil: 'networkidle2',
      timeout: 30000
    });

    console.log('✅ WhatsApp Web loaded');
  }

  async injectScript() {
    console.log('💉 Injecting WhatsApp scripts...');
    
    try {
      // Inject the script
      await this.page.evaluateOnNewDocument((script) => {
        eval(script);
      }, this.injectionScript);

      // Also inject it immediately
      await this.page.evaluate((script) => {
        eval(script);
      }, this.injectionScript);

      console.log('✅ Script injected successfully');
    } catch (error) {
      console.error('❌ Failed to inject script:', error);
      throw error;
    }
  }

  async waitForQRCode() {
    console.log('📱 Checking login status...');

    try {
      // First check if already logged in (persistent session)
      const isLoggedIn = await this.page.evaluate(() => {
        return !!(document.querySelector('#main') || document.querySelector('#side'));
      });

      if (isLoggedIn) {
        console.log('✅ Already logged in from previous session!');
        return false; // No QR code needed
      }

      // Wait for QR code container
      await this.page.waitForSelector('div[data-ref]', { timeout: 30000 });
      console.log('✅ QR code appeared');
      console.log('👆 Please scan the QR code with your phone to continue...');

      return true;
    } catch (error) {
      console.log('⚠️  QR code not found, checking if already logged in...');
      return false;
    }
  }

  async waitForLogin() {
    console.log('⏳ Waiting for login to complete...');
    
    try {
      // Wait for the main chat interface to appear
      await this.page.waitForSelector('#main, #side', { timeout: 120000 }); // 2 minutes timeout
      console.log('✅ Login successful! Main interface loaded');

      // Wait a bit more for the injection script to fully initialize
      console.log('⏳ Waiting for WhatsApp scripts to initialize...');
      await this.page.waitForFunction(() => {
        return window.__fidelizza__ && window.__fidelizza__.isFullyLoaded;
      }, { timeout: 60000 });

      console.log('✅ WhatsApp scripts initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ Login timeout or script initialization failed:', error);
      return false;
    }
  }

  async testSendMessage(phoneNumber, message) {
    console.log(`📤 Testing message sending to ${phoneNumber}...`);
    
    try {
      const result = await this.page.evaluate(async (phone, msg) => {
        // Check if the API is available
        if (!window.__fidelizza__ || !window.__fidelizza__.chats || !window.__fidelizza__.contacts) {
          throw new Error('WhatsApp scripts API not available');
        }

        try {
          // Convert phone number to contact ID
          const contactId = window.__fidelizza__.contacts.numberToContactId(phone);
          console.log('Contact ID:', contactId);

          // Send the message
          const messageResult = await window.__fidelizza__.chats.sendMessageToId(contactId, msg);
          console.log('Message sent:', messageResult);

          return {
            success: true,
            contactId: contactId,
            messageId: messageResult?.id || 'unknown',
            message: msg
          };
        } catch (error) {
          console.error('Error in message sending:', error);
          return {
            success: false,
            error: error.message
          };
        }
      }, phoneNumber, message);

      if (result.success) {
        console.log('✅ Message sent successfully!');
        console.log(`   Contact ID: ${result.contactId}`);
        console.log(`   Message ID: ${result.messageId}`);
        console.log(`   Message: "${result.message}"`);
        return true;
      } else {
        console.error('❌ Failed to send message:', result.error);
        return false;
      }
    } catch (error) {
      console.error('❌ Error during message test:', error);
      return false;
    }
  }

  async testAPI() {
    console.log('🧪 Testing WhatsApp API availability...');
    
    const apiStatus = await this.page.evaluate(() => {
      const api = window.__fidelizza__;
      if (!api) return { available: false, reason: 'API not found' };
      
      return {
        available: true,
        modules: {
          chats: !!api.chats,
          contacts: !!api.contacts,
          messages: !!api.messages,
          status: !!api.status
        },
        version: api.srcVersion || 'unknown',
        isFullyLoaded: api.isFullyLoaded()
      };
    });

    console.log('📊 API Status:', JSON.stringify(apiStatus, null, 2));
    return apiStatus.available;
  }

  async cleanup() {
    console.log('🧹 Cleaning up...');
    if (this.browser) {
      try {
        await this.browser.close();
      } catch (error) {
        console.log('⚠️  Browser cleanup warning:', error.message);
      }
    }
  }

  clearProfile() {
    if (this.userDataDir && fs.existsSync(this.userDataDir)) {
      console.log('🗑️  Clearing browser profile...');
      try {
        // Remove SingletonLock file if it exists
        const lockFile = path.join(this.userDataDir, 'SingletonLock');
        if (fs.existsSync(lockFile)) {
          fs.unlinkSync(lockFile);
        }
        fs.rmSync(this.userDataDir, { recursive: true, force: true });
        console.log('✅ Profile cleared');
      } catch (error) {
        console.log('⚠️  Profile clear warning:', error.message);
      }
    }
  }

  async ensureProfileAvailable() {
    if (!this.userDataDir) return;

    // Remove lock file if it exists (from previous crashed sessions)
    const lockFile = path.join(this.userDataDir, 'SingletonLock');
    if (fs.existsSync(lockFile)) {
      console.log('🔓 Removing stale profile lock...');
      try {
        fs.unlinkSync(lockFile);
      } catch (error) {
        console.log('⚠️  Could not remove lock file:', error.message);
      }
    }
  }

  async run(phoneNumber, testMessage = 'Hello from WhatsApp Scripts Test! 🤖') {
    try {
      await this.init();
      await this.openWhatsAppWeb();
      await this.injectScript();

      const qrVisible = await this.waitForQRCode();
      if (qrVisible) {
        console.log('📱 Scan the QR code and press Enter to continue...');
        // Wait for user input
        await new Promise(resolve => {
          process.stdin.once('data', () => resolve());
        });
      } else if (this.options.persistSession) {
        console.log('🔄 Using existing login session...');
      }

      const loginSuccess = await this.waitForLogin();
      if (!loginSuccess) {
        throw new Error('Login failed or timed out');
      }

      await this.testAPI();

      if (phoneNumber) {
        await this.testSendMessage(phoneNumber, testMessage);
      } else {
        console.log('📝 No phone number provided, skipping message test');
        console.log('💡 You can test manually using: window.__fidelizza__.chats.sendMessageToId(contactId, "message")');
      }

      console.log('✅ Test completed successfully!');
      console.log('🔧 Browser will remain open for manual testing. Close it when done.');

      if (this.options.persistSession) {
        console.log('💾 Session will be saved for next time!');
      }

      // Keep browser open for manual testing
      console.log('Press Enter to close the browser...');
      await new Promise(resolve => {
        process.stdin.once('data', () => resolve());
      });

    } catch (error) {
      console.error('❌ Test failed:', error);
    } finally {
      await this.cleanup();
    }
  }
}

// CLI usage
if (require.main === module) {
  const args = process.argv.slice(2);

  // Parse command line arguments
  let phoneNumber = null;
  let message = 'Hello from WhatsApp Scripts Test! 🤖';
  let options = {};

  // Handle flags
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    if (arg === '--no-persist' || arg === '--fresh') {
      options.persistSession = false;
    } else if (arg === '--clear-profile') {
      const test = new WhatsAppPuppeteerTest();
      test.clearProfile();
      console.log('✅ Profile cleared. Run the test again to start fresh.');
      process.exit(0);
    } else if (arg === '--profile') {
      options.profileName = args[++i];
    } else if (!phoneNumber && arg.startsWith('+')) {
      phoneNumber = arg;
    } else if (phoneNumber && !message.includes('🤖')) {
      message = arg;
    }
  }

  console.log('🎯 WhatsApp Web Puppeteer Test');
  console.log('===============================');

  if (options.persistSession === false) {
    console.log('🔄 Running in fresh mode (no session persistence)');
  } else {
    console.log(`💾 Using persistent profile: ${options.profileName || 'whatsapp-test'}`);
  }

  if (phoneNumber) {
    console.log(`📞 Target phone: ${phoneNumber}`);
    console.log(`💬 Test message: "${message}"`);
  } else {
    console.log('💡 Usage: node whatsapp-puppeteer-test.js [options] [phone_number] [message]');
    console.log('📝 Running without phone number - API testing only');
  }

  console.log('');
  console.log('Options:');
  console.log('  --no-persist, --fresh    Start fresh (don\'t save/load session)');
  console.log('  --clear-profile          Clear saved profile and exit');
  console.log('  --profile <name>         Use custom profile name');
  console.log('');

  const test = new WhatsAppPuppeteerTest(options);
  test.run(phoneNumber, message);
}

module.exports = WhatsAppPuppeteerTest;
